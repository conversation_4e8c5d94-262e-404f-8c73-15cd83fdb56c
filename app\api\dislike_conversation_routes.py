from flask import Blueprint, request, jsonify
from flask_restx import Api, Resource, Namespace, fields
from app.models.dislike_conversation_dao import DislikeConversationDAO
from app.models.dislike_conversation_config_dao import DislikeConversationConfigDAO
import json

# 创建命名空间
dislike_conversation_ns = Namespace('dislike_conversations', description='点踩对话记录管理接口')

# 定义消息模型
message_model = dislike_conversation_ns.model('DislikeMessage', {
    'role': fields.String(required=True, description='消息类型 (assistant/user)'),
    'content': fields.String(required=True, description='消息内容'),
    'reasoning': fields.String(required=False, description='推理过程')
})

# 定义点踩对话记录模型
dislike_conversation_model = dislike_conversation_ns.model('DislikeConversation', {
    'conversationId': fields.Integer(required=True, description='对话记录ID'),
    'error_code': fields.String(required=False, description='错误代码'),
    'error_content': fields.String(required=False, description='错误内容'),
    'messages': fields.List(fields.Nested(message_model), required=True, description='消息列表')
})

# 定义配置项模型
config_model = dislike_conversation_ns.model('DislikeConfig', {
    'id': fields.Integer(readonly=True, description='配置项ID'),
    'code': fields.String(required=True, description='错误代码'),
    'name': fields.String(required=True, description='错误名称')
})

# 定义响应模型
response_model = dislike_conversation_ns.model('Response', {
    'code': fields.Integer(required=True, description='响应代码'),
    'message': fields.String(required=True, description='响应消息'),
    'data': fields.Raw(description='响应数据')
})

@dislike_conversation_ns.route('/config')
class DislikeConversationConfig(Resource):
    @dislike_conversation_ns.doc('获取点踩配置项')
    @dislike_conversation_ns.marshal_with(response_model)
    def get(self):
        """获取点踩配置项"""
        try:
            # 初始化默认配置项
            DislikeConversationConfigDAO.init_default_configs()
            
            # 获取所有配置项
            configs = DislikeConversationConfigDAO.get_all_configs()
            
            return {
                'code': 200,
                'message': '配置项获取成功',
                'data': configs
            }, 200
        except Exception as e:
            return {
                'code': 500,
                'message': f'服务器错误: {str(e)}',
                'data': None
            }, 500

@dislike_conversation_ns.route('')
class DislikeConversationList(Resource):
    @dislike_conversation_ns.doc('点踩对话记录')
    @dislike_conversation_ns.expect(dislike_conversation_model)
    @dislike_conversation_ns.marshal_with(response_model)
    def post(self):
        """点踩对话记录"""
        try:
            data = request.json
            
            # 验证必要参数
            if 'conversationId' not in data or 'messages' not in data:
                return {
                    'code': 400,
                    'message': '缺少必要参数',
                    'data': None
                }, 400
            
            # 验证error_code或error_content至少有一个
            if 'error_code' not in data and 'error_content' not in data:
                return {
                    'code': 400,
                    'message': 'error_code与error_content必须传1个',
                    'data': None
                }, 400
            
            # 创建点踩记录
            dislike_conversation = DislikeConversationDAO.create_dislike_conversation(data)
            
            return {
                'code': 200,
                'message': '点踩对话记录成功',
                'data': None
            }, 200
        except Exception as e:
            return {
                'code': 500,
                'message': f'服务器错误: {str(e)}',
                'data': None
            }, 500

@dislike_conversation_ns.route('/conversation/<int:conversation_id>')
@dislike_conversation_ns.param('conversation_id', '对话记录ID')
class DislikeConversationByConversation(Resource):
    @dislike_conversation_ns.doc('获取指定对话的点踩记录')
    @dislike_conversation_ns.marshal_with(response_model)
    def get(self, conversation_id):
        """获取指定对话的点踩记录"""
        try:
            # 获取点踩记录
            dislike_conversations = DislikeConversationDAO.get_dislike_conversations_by_conversation_id(conversation_id)
            
            # 返回完整的点踩记录信息，包括id和messages
            result = []
            for dislike in dislike_conversations:
                result.append({
                    'id': dislike['id'],
                    'messages': dislike['messages']
                })
            
            return {
                'code': 200,
                'message': '查询点踩对话记录成功',
                'data': result
            }, 200
        except Exception as e:
            return {
                'code': 500,
                'message': f'服务器错误: {str(e)}',
                'data': None
            }, 500

@dislike_conversation_ns.route('/<int:dislike_id>')
@dislike_conversation_ns.param('dislike_id', '点踩记录ID')
class DislikeConversationItem(Resource):
    @dislike_conversation_ns.doc('获取点踩记录详情')
    @dislike_conversation_ns.marshal_with(response_model)
    def get(self, dislike_id):
        """获取点踩记录详情"""
        try:
            # 获取点踩记录
            dislike_conversation = DislikeConversationDAO.get_dislike_conversation_by_id(dislike_id)
            
            if not dislike_conversation:
                return {
                    'code': 404,
                    'message': '点踩记录不存在',
                    'data': None
                }, 404
            
            # 返回完整的点踩记录信息
            result = {
                'id': dislike_conversation['id'],
                'messages': dislike_conversation['messages']
            }
            
            return {
                'code': 200,
                'message': '查询点踩记录成功',
                'data': result
            }, 200
        except Exception as e:
            return {
                'code': 500,
                'message': f'服务器错误: {str(e)}',
                'data': None
            }, 500
            
    @dislike_conversation_ns.doc('取消点踩对话记录')
    @dislike_conversation_ns.marshal_with(response_model)
    def delete(self, dislike_id):
        """取消点踩对话记录"""
        try:
            # 删除点踩记录
            result = DislikeConversationDAO.delete_dislike_conversation(dislike_id)
            
            if not result:
                return {
                    'code': 404,
                    'message': '点踩记录不存在',
                    'data': None
                }, 404
            
            return {
                'code': 200,
                'message': '取消点踩对话记录成功',
                'data': None
            }, 200
        except Exception as e:
            return {
                'code': 500,
                'message': f'服务器错误: {str(e)}',
                'data': None
            }, 500 