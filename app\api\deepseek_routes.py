from flask_restx import Namespace, Resource, fields
from flask import request, Response, stream_with_context, jsonify
from app.models.deepseek_model import DeepseekModel
from app.config import Config  # 修改为从Config类导入
import logging
from datetime import datetime
import json

# 创建命名空间
deepseek_ns = Namespace('AI模型', description='AI模型接口')

# 定义请求和响应模型
message_model = deepseek_ns.model('Message', {
    'role': fields.String(required=True, description='消息角色(system/user/assistant)'),
    'content': fields.Raw(required=True, description='消息内容，支持任意数据类型')
})

chat_request = deepseek_ns.model('ChatRequest', {
    'messages': fields.List(fields.Nested(message_model), required=True, description='对话消息列表'),
    'stream': fields.Boolean(default=False, description='是否使用流式输出'),
    'temperature': fields.Float(default=1.0, description='采样温度(0-2之间)'),
    'max_tokens': fields.Integer(description='最大生成token数'),
    'provider': fields.String(description='模型提供商(siliconflow/deepseek/spark)', default='deepseek')
})

chat_response = deepseek_ns.model('ChatResponse', {
    'content': fields.String(description='AI回复内容'),
    'usage': fields.Raw(description='token使用统计')
})

@deepseek_ns.route('/test')
class TestAPI(Resource):
    @deepseek_ns.response(200, 'Success')
    @deepseek_ns.response(500, 'Internal Server Error')
    def get(self):
        """测试服务是否正常运行的接口"""
        try:
            return {
                'status': 'ok',
                'message': 'DeepSeek API service is running',
                'timestamp': datetime.now().isoformat(),
            }
        except Exception as e:
            logging.error(f"Test API错误: {str(e)}")
            return {'error': str(e)}, 500 
  
@deepseek_ns.route('/chat')
class ChatAPI(Resource):
    @deepseek_ns.expect(chat_request)
    @deepseek_ns.response(200, 'Success', chat_response)
    @deepseek_ns.response(500, 'Internal Server Error')
    def post(self):
        """DeepSeek聊天接口"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "无效的请求数据"}), 400
                
            provider = data.get('provider')
            messages = data.get('messages', [])
            
            # 验证必要参数
            if not messages:
                return jsonify({"error": "消息列表不能为空"}), 400
                
            deepseek = DeepseekModel(provider)
            stream = data.get('stream', False)
            temperature = data.get('temperature', 1.0)
            max_tokens = data.get('max_tokens', 2000)
            
            try:
                response = deepseek.chat_completion(
                    messages=messages,
                    stream=stream,
                    temperature=temperature,
                    max_tokens=max_tokens
                )
                
                if stream:
                    def generate():
                        try:
                            for chunk in response:
                                if chunk:
                                    # 添加心跳保持连接
                                    yield f"data: {chunk}\n\n"
                            # 添加结束标记
                            yield "data: [DONE]\n\n"
                        except Exception as e:
                            logging.error(f"Stream error: {str(e)}")
                            error_response = json.dumps({
                                "status": 2,
                                "error": str(e),
                                "choices": [{
                                    "index": 0,
                                    "content": "",
                                    "finish_reason": "error"
                                }]
                            }, ensure_ascii=False)
                            yield f"data: {error_response}\n\n"
                            yield "data: [DONE]\n\n"

                    return Response(
                        stream_with_context(generate()),
                        mimetype='text/event-stream',
                        headers={
                            'Cache-Control': 'no-cache',
                            'X-Accel-Buffering': 'no',
                            'Content-Type': 'text/event-stream',
                            'Connection': 'keep-alive',
                            'Access-Control-Allow-Origin': '*',
                            'Transfer-Encoding': 'chunked'
                        }
                    )
                
                return jsonify(response)
                
            except Exception as e:
                logging.error(f"模型调用错误: {str(e)}")
                return jsonify({"error": f"模型调用失败: {str(e)}"}), 500
                
        except Exception as e:
            logging.error(f"Chat API错误: {str(e)}")
            return jsonify({"error": str(e)}), 500

@deepseek_ns.route('/reasoning')
class ReasoningAPI(Resource):
    @deepseek_ns.expect(chat_request)
    @deepseek_ns.response(200, 'Success', chat_response)
    @deepseek_ns.response(500, 'Internal Server Error')
    def post(self):
        """DeepSeek推理模型接口"""
        try:
            data = request.get_json()
            provider = data.get('provider')
            deepseek = DeepseekModel(provider)
            messages = data.get('messages', [])
            stream = data.get('stream', False)
            temperature = data.get('temperature', 1.0)
            max_tokens = data.get('max_tokens', 5000)
            
            response = deepseek.reasoning_completion(
                messages=messages,
                stream=stream,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            if stream:
                def generate():
                    try:
                        for chunk in response:
                            if chunk:
                                # 添加心跳保持连接
                                yield f"data: {chunk}\n\n"
                        # 添加结束标记
                        yield "data: [DONE]\n\n"
                    except Exception as e:
                        logging.error(f"Stream error: {str(e)}")
                        error_response = json.dumps({
                            "status": 2,
                            "error": str(e),
                            "choices": [{
                                "index": 0,
                                "content": "",
                                "finish_reason": "error"
                            }]
                        }, ensure_ascii=False)
                        yield f"data: {error_response}\n\n"
                        yield "data: [DONE]\n\n"

                return Response(
                    stream_with_context(generate()),
                    mimetype='text/event-stream',
                    headers={
                        'Cache-Control': 'no-cache',
                        'X-Accel-Buffering': 'no',
                        'Content-Type': 'text/event-stream',
                        'Connection': 'keep-alive',
                        'Access-Control-Allow-Origin': '*',
                        'Transfer-Encoding': 'chunked'
                    }
                )
                
            return jsonify(response)
            
        except Exception as e:
            logging.error(f"Reasoning API错误: {str(e)}")
            return jsonify({"error": str(e)}), 500