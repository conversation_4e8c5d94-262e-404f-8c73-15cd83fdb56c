from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean
from sqlalchemy.dialects.mysql import LONGTEXT
from sqlalchemy.ext.declarative import declarative_base
from app.models.conversation import Base
import datetime

class WechatScanUpload(Base):
    """微信扫码上传图片记录模型"""
    __tablename__ = 'wechat_scan_upload'

    id = Column(Integer, primary_key=True, autoincrement=True)
    scan_id = Column(String(64), nullable=False, unique=True, index=True)  # 扫码ID，唯一
    image_base64 = Column(LONGTEXT, nullable=True)  # 图片的base64数据
    is_consumed = Column(Boolean, default=False)  # 是否已被消费（查询过）
    create_time = Column(DateTime, default=datetime.datetime.now)
    update_time = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    def to_dict(self):
        """将模型转换为字典"""
        return {
            'id': self.id,
            'scanId': self.scan_id,
            'imageBase64': self.image_base64,
            'isConsumed': self.is_consumed,
            'createTime': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None,
            'updateTime': self.update_time.strftime('%Y-%m-%d %H:%M:%S') if self.update_time else None
        }

    @staticmethod
    def from_dict(data):
        """从字典创建模型实例"""
        wechat_scan_upload = WechatScanUpload(
            scan_id=data.get('scanId'),
            image_base64=data.get('imageBase64'),
            is_consumed=data.get('isConsumed', False)
        )
        if 'id' in data:
            wechat_scan_upload.id = data['id']
        return wechat_scan_upload 