from app.models.log_generate_video import LogGenerateVideo
from app.models.database import SessionLocal
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime, timedelta
from sqlalchemy import func

class LogGenerateVideoDAO:
    """视频生成日志数据访问对象"""

    @staticmethod
    def create_log(data):
        """创建新的日志记录"""
        db = SessionLocal()
        try:
            log = LogGenerateVideo.from_dict(data)
            db.add(log)
            db.commit()
            db.refresh(log)
            return log.to_dict()
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def get_today_count_by_uid(uid):
        """获取指定用户今天的生成次数"""
        db = SessionLocal()
        try:
            today = datetime.now().date()
            tomorrow = today + timedelta(days=1)
            
            count = db.query(func.count(LogGenerateVideo.id)).filter(
                LogGenerateVideo.uid == uid,
                LogGenerateVideo.create_time >= today,
                LogGenerateVideo.create_time < tomorrow
            ).scalar()
            
            return count
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close() 