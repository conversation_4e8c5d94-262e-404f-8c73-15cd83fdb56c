from app.models.dislike_conversation import DislikeConversation
from app.models.database import <PERSON>L<PERSON>al
from sqlalchemy.exc import SQLAlchemyError

class DislikeConversationDAO:
    """点踩对话记录数据访问对象"""

    @staticmethod
    def get_dislike_conversations_by_conversation_id(conversation_id):
        """获取指定对话ID的所有点踩记录"""
        db = SessionLocal()
        try:
            dislike_conversations = db.query(DislikeConversation).filter(
                DislikeConversation.conversation_id == conversation_id
            ).all()
            return [dislike_conversation.to_dict() for dislike_conversation in dislike_conversations]
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def create_dislike_conversation(dislike_conversation_data):
        """创建新的点踩记录"""
        db = SessionLocal()
        try:
            dislike_conversation = DislikeConversation.from_dict(dislike_conversation_data)
            db.add(dislike_conversation)
            db.commit()
            db.refresh(dislike_conversation)
            return dislike_conversation.to_dict()
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def get_dislike_conversation_by_id(dislike_conversation_id):
        """根据ID获取点踩记录"""
        db = SessionLocal()
        try:
            dislike_conversation = db.query(DislikeConversation).filter(
                DislikeConversation.id == dislike_conversation_id
            ).first()
            if dislike_conversation:
                return dislike_conversation.to_dict()
            return None
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def delete_dislike_conversation(dislike_conversation_id):
        """删除点踩记录"""
        db = SessionLocal()
        try:
            dislike_conversation = db.query(DislikeConversation).filter(
                DislikeConversation.id == dislike_conversation_id
            ).first()
            if not dislike_conversation:
                return False
            
            db.delete(dislike_conversation)
            db.commit()
            return True
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close() 