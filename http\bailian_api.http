### 测试阿里云百炼服务状态
GET http://localhost:5000/api/bailian/test
Content-Type: application/json

###

### 阿里云百炼聊天接口 - 基础对话
POST http://localhost:5000/api/bailian/chat
Content-Type: application/json

{
    "messages": [
        {
            "role": "user",
            "content": "你好，请介绍一下你自己"
        }
    ]
}

###

### 阿里云百炼聊天接口 - 多轮对话
POST http://localhost:5000/api/bailian/chat
Content-Type: application/json

{
    "messages": [
        {
            "role": "user",
            "content": "你好"
        },
        {
            "role": "assistant",
            "content": "你好！我是通义千问，由阿里云开发的AI助手。我被设计用来回答各种问题、提供信息和与用户进行对话。有什么我可以帮助你的吗？"
        },
        {
            "role": "user",
            "content": "请帮我写一首关于春天的诗"
        }
    ]
}

###

### 阿里云百炼聊天接口 - 包含图片
POST http://localhost:5000/api/bailian/chat
Content-Type: application/json

{
    "messages": [
        {
            "role": "user",
            "content": "请描述一下这张图片"
        }
    ],
    "image_list": [
        "https://example.com/image1.jpg",
        "https://example.com/image2.jpg"
    ]
}

###

### 阿里云百炼聊天接口 - 错误测试（空消息）
POST http://localhost:5000/api/bailian/chat
Content-Type: application/json

{
    "messages": []
}

###

### 阿里云百炼聊天接口 - 错误测试（无效格式）
POST http://localhost:5000/api/bailian/chat
Content-Type: application/json

{
    "messages": "invalid format"
}

###
