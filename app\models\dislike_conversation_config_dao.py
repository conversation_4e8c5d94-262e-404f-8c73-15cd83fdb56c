from app.models.dislike_conversation_config import DislikeConversationConfig
from app.models.database import SessionLocal
from sqlalchemy.exc import SQLAlchemyError

class DislikeConversationConfigDAO:
    """点踩配置项数据访问对象"""

    @staticmethod
    def get_all_configs():
        """获取所有配置项"""
        db = SessionLocal()
        try:
            configs = db.query(DislikeConversationConfig).all()
            return [config.to_dict() for config in configs]
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def create_config(config_data):
        """创建新的配置项"""
        db = SessionLocal()
        try:
            config = DislikeConversationConfig.from_dict(config_data)
            db.add(config)
            db.commit()
            db.refresh(config)
            return config.to_dict()
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def get_config_by_id(config_id):
        """根据ID获取配置项"""
        db = SessionLocal()
        try:
            config = db.query(DislikeConversationConfig).filter(
                DislikeConversationConfig.id == config_id
            ).first()
            if config:
                return config.to_dict()
            return None
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def get_config_by_code(code):
        """根据代码获取配置项"""
        db = SessionLocal()
        try:
            config = db.query(DislikeConversationConfig).filter(
                DislikeConversationConfig.code == code
            ).first()
            if config:
                return config.to_dict()
            return None
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def init_default_configs():
        """初始化默认配置项"""
        default_configs = [
            {"code": "understand_error", "name": "理解错误"},
            {"code": "reasoning_error", "name": "推理错误"},
            {"code": "format_error", "name": "格式错误"},
            {"code": "content_error", "name": "内容不专业"},
            {"code": "other_error", "name": "其他"}
        ]
        
        db = SessionLocal()
        try:
            # 检查是否已有配置项
            count = db.query(DislikeConversationConfig).count()
            if count == 0:
                # 如果没有配置项，则初始化
                for config_data in default_configs:
                    config = DislikeConversationConfig.from_dict(config_data)
                    db.add(config)
                db.commit()
                return True
            return False
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close() 