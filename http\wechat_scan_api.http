### 微信扫码上传图片API测试

### 1. 生成扫码ID接口
POST http://localhost:5000/api/wechat-scan/generate-scan-id
Content-Type: application/json

### 预期响应:
### {
###   "code": 200,
###   "msg": "生成扫码ID成功",
###   "data": {
###     "scanId": "生成的扫码ID"
###   }
### }

### 2. 绑定图片到扫码ID接口
POST http://localhost:5000/api/wechat-scan/bind-image
Content-Type: application/json

{
  "scanId": "请替换为第一步生成的扫码ID",
  "imageBase64": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="
}

### 预期响应:
### {
###   "code": 200,
###   "msg": "图片绑定成功",
###   "data": null
### }

### 3. 通过扫码ID查询绑定的图片接口
GET http://localhost:5000/api/wechat-scan/query-image/请替换为扫码ID

### 预期响应（第一次查询成功）:
### {
###   "code": 200,
###   "msg": "查询成功",
###   "data": {
###     "imageBase64": "图片的base64数据"
###   }
### }

### 预期响应（第二次查询失败）:
### {
###   "code": 200,
###   "msg": "未找到绑定的图片或图片已被查询过",
###   "data": null
### }

### 4. 清理过期记录接口（维护接口）
POST http://localhost:5000/api/wechat-scan/clean-expired
Content-Type: application/json

### 预期响应:
### {
###   "code": 200,
###   "msg": "清理完成",
###   "data": {
###     "deletedCount": 清理的记录数量
###   }
### }

### ===== 完整的测试流程示例 =====

### 步骤1: 生成扫码ID
POST http://localhost:5000/api/wechat-scan/generate-scan-id
Content-Type: application/json

###

### 步骤2: 绑定图片（使用步骤1返回的scanId）
POST http://localhost:5000/api/wechat-scan/bind-image
Content-Type: application/json

{
  "scanId": "示例scanId_请替换",
  "imageBase64": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="
}

###

### 步骤3: 第一次查询图片（应该成功）
GET http://localhost:5000/api/wechat-scan/query-image/示例scanId_请替换

###

### 步骤4: 第二次查询图片（应该失败，显示已被查询过）
GET http://localhost:5000/api/wechat-scan/query-image/示例scanId_请替换

###

### 步骤5: 重新绑定图片到同一个scanId
POST http://localhost:5000/api/wechat-scan/bind-image
Content-Type: application/json

{
  "scanId": "示例scanId_请替换",
  "imageBase64": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="
}

###

### 步骤6: 再次查询图片（应该成功，因为重新绑定了）
GET http://localhost:5000/api/wechat-scan/query-image/示例scanId_请替换 