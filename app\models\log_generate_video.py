from sqlalchemy import Column, Integer, String, DateTime
from app.models.conversation import Base
import datetime

class LogGenerateVideo(Base):
    """视频生成日志模型"""
    __tablename__ = 'log_generate_video'

    id = Column(Integer, primary_key=True, autoincrement=True)
    uid = Column(String(50), nullable=False, index=True)  # 用户唯一标识
    prompt = Column(String(255), nullable=False)  # 提示词
    model = Column(String(50), nullable=False)  # 模型
    create_time = Column(DateTime, default=datetime.datetime.now)

    def to_dict(self):
        """将模型转换为字典"""
        return {
            'id': self.id,
            'uid': self.uid,
            'prompt': self.prompt,
            'model': self.model,
            'createTime': self.create_time.strftime('%Y-%m-%d %H:%M:%S')
        }

    @staticmethod
    def from_dict(data):
        """从字典创建模型实例"""
        log = LogGenerateVideo(
            uid=data.get('uid'),
            prompt=data.get('prompt'),
            model=data.get('model')
        )
        if 'id' in data:
            log.id = data['id']
        return log 