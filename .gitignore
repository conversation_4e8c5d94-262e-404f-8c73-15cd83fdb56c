# IDE 配置文件
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# 依赖目录
node_modules/
venv/
__pycache__/
*.pyc

# 环境配置
.env
.env.local
.env.*.local

# 日志文件
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 操作系统生成的文件
.DS_Store
Thumbs.db

# 构建输出目录
/dist/
/build/
/out/

# 临时文件
*.swp
*.swo
*~
.tmp/
temp/

# 测试覆盖率报告
coverage/

# 本地配置文件
config.local.js
*.local.json

# 包管理器锁定文件（根据需要取消注释）
# package-lock.json
# yarn.lock 