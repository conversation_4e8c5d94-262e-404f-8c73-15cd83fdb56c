import os
from dotenv import load_dotenv
from typing import Dict, Any

load_dotenv()

class ModelConfig:
    """模型配置类"""
    def __init__(self, api_key: str, api_base: str, chat_model: str, reasoner_model: str):
        self.api_key = api_key
        self.api_base = api_base
        self.chat_model = chat_model
        self.reasoner_model = reasoner_model

class Config:
    # API服务配置
    HOST = "0.0.0.0"
    PORT = int(os.getenv('PORT', 5000))  # 从环境变量读取端口，默认5000
    DEBUG = os.getenv('FLASK_ENV') == 'development'  # 根据环境设置调试模式
    
    SILICON_FLOW_API_KEY = 'sk-nwwalgfszwcjsvbaxturprdhoueywjipjnsuswseyayuzknl'

    # 模型配置映射
    MODEL_CONFIGS: Dict[str, ModelConfig] = {
        'siliconflow': ModelConfig(
            api_key=SILICON_FLOW_API_KEY,
            api_base='https://api.siliconflow.cn/v1',
            chat_model='Pro/deepseek-ai/DeepSeek-V3',
            reasoner_model='Pro/deepseek-ai/DeepSeek-R1'
        ),
        'deepseek': ModelConfig(
            api_key='***********************************',
            api_base='https://api.deepseek.com/v1',
            chat_model='deepseek-chat',
            reasoner_model='deepseek-reasoner'
        ),
        'spark': ModelConfig(
            api_key='WDFxEuxnfFhgbyAptdzp:zBVUPUzuCKDqddzjnHVO',
            api_base='https://spark-api-open.xf-yun.com/v1',
            chat_model='generalv3.5',
            reasoner_model='4.0Ultra'
        ),
        'qwen': ModelConfig(
            api_key='sk-fa7b064d2bd440fea8a8a463d3c6d16d',
            api_base='https://dashscope.aliyuncs.com/compatible-mode/v1',
            chat_model='qwen-vl-max-latest',
            reasoner_model='qvq-max'
        )
    }
    
    # 阿里云百炼应用配置
    BAILIAN_CONFIG = {
        'api_key': 'sk-df2a2d19b36744679defba4b2141debe',
        'app_id': 'f3c4c38f2fe74629a8a7b6b635e9eb0c',
        'workspace': 'llm-9tcgl5onm1w2cbl0'
    }
    
    # 默认使用的模型配置
    DEFAULT_PROVIDER = 'deepseek'
    
    @classmethod
    def get_model_config(cls, provider: str = None) -> ModelConfig:
        """获取指定提供商的模型配置"""
        provider = provider or cls.DEFAULT_PROVIDER
        if provider not in cls.MODEL_CONFIGS:
            raise ValueError(f"不支持的模型提供商: {provider}")
        return cls.MODEL_CONFIGS[provider] 