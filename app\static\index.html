<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>DeepSeek API 演示</title>
    <style>
        body {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .panel {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
        }
        .chat-box {
            height: 300px;
            border: 1px solid #ccc;
            padding: 10px;
            margin: 10px 0;
            overflow-y: auto;
            background: #f9f9f9;
        }
        .input-area {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        textarea {
            flex: 1;
            height: 60px;
            padding: 8px;
        }
        button {
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
        button:disabled {
            background: #cccccc;
        }
        .user-message {
            background: #e3f2fd;
            padding: 8px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .ai-message {
            background: #f5f5f5;
            padding: 8px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .reasoning-process {
            background: #fff3e0;
            padding: 8px;
            margin: 5px 0;
            border-radius: 4px;
            font-style: italic;
            color: #666;
        }
        .controls {
            margin: 10px 0;
        }
        label {
            margin-right: 10px;
        }
        .provider-select {
            padding: 5px;
            margin-right: 10px;
            border-radius: 4px;
            border: 1px solid #ccc;
        }
    </style>
</head>
<body>
    <h1>DeepSeek API 演示</h1>
    
    <div class="container">
        <!-- 聊天模型面板 -->
        <div class="panel">
            <h2>Chat 模型</h2>
            <div class="controls">
                <label>
                    模型提供商:
                    <select id="chatProvider" class="provider-select">
                        <option value="deepseek">深度求索</option>
                        <option value="siliconflow">硅基流动</option>
                        <option value="spark">讯飞星火</option>
                    </select>
                </label>
                <label>
                    <input type="checkbox" id="chatStream"> 流式输出
                </label>
                <label>
                    温度:
                    <input type="number" id="chatTemp" value="0.7" min="0" max="2" step="0.1">
                </label>
            </div>
            <div class="chat-box" id="chatOutput"></div>
            <div class="input-area">
                <textarea id="chatInput" placeholder="输入消息..."></textarea>
                <button onclick="sendChat()" id="chatButton">发送</button>
            </div>
        </div>

        <!-- 推理模型面板 -->
        <div class="panel">
            <h2>Reasoning 模型</h2>
            <div class="controls">
                <label>
                    模型提供商:
                    <select id="reasoningProvider" class="provider-select">
                        <option value="deepseek">深度求索</option>
                        <option value="siliconflow">硅基流动</option>
                        <option value="spark">讯飞星火</option>
                    </select>
                </label>
                <label>
                    <input type="checkbox" id="reasoningStream"> 流式输出
                </label>
                <label>
                    温度:
                    <input type="number" id="reasoningTemp" value="0.3" min="0" max="2" step="0.1">
                </label>
            </div>
            <div class="chat-box" id="reasoningOutput"></div>
            <div class="input-area">
                <textarea id="reasoningInput" placeholder="输入问题..."></textarea>
                <button onclick="sendReasoning()" id="reasoningButton">发送</button>
            </div>
        </div>
    </div>

    <script>
        // 工具函数
        function appendMessage(containerId, message, isUser) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = isUser ? 'user-message' : 'ai-message';
            div.textContent = message;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
            return div;
        }

        async function streamResponse(response, outputId) {
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let aiMessageDiv = null;
            let reasoningDiv = null;
            let accumulatedText = '';
            let accumulatedReasoning = '';
            
            try {
                aiMessageDiv = appendMessage(outputId, '', false);
                
                while (true) {
                    const {value, done} = await reader.read();
                    
                    if (done) break;
                    
                    const text = decoder.decode(value);
                    const lines = text.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6).trim();
                            
                            // 检查是否是结束标记
                            if (data === '[DONE]') {
                                console.log('Stream completed normally');
                                return;
                            }
                            
                            try {
                                const jsonData = JSON.parse(data);
                                
                                if (jsonData.error) {
                                    aiMessageDiv.textContent += '\n[Error: ' + jsonData.error + ']';
                                    return;
                                }
                                
                                if (jsonData.choices && jsonData.choices[0]) {
                                    // 处理推理过程
                                    if (jsonData.choices[0].reasoning_content) {
                                        if (!reasoningDiv) {
                                            reasoningDiv = document.createElement('div');
                                            reasoningDiv.className = 'reasoning-process';
                                            aiMessageDiv.parentNode.insertBefore(reasoningDiv, aiMessageDiv);
                                        }
                                        accumulatedReasoning += jsonData.choices[0].reasoning_content;
                                        reasoningDiv.textContent = '思考过程：\n' + accumulatedReasoning;
                                    }
                                    
                                    // 处理内容
                                    if (jsonData.choices[0].content) {
                                        accumulatedText += jsonData.choices[0].content;
                                        aiMessageDiv.textContent = accumulatedText;
                                    }
                                }
                            } catch (error) {
                                console.error('JSON parse error:', error);
                            }
                        }
                    }
                }
            } catch (error) {
                console.error('Stream processing error:', error);
                if (aiMessageDiv) {
                    aiMessageDiv.textContent += '\n[Error: 流式输出处理错误]';
                }
            }
        }

        // Chat模型
        async function sendChat() {
            const input = document.getElementById('chatInput');
            const button = document.getElementById('chatButton');
            const isStream = document.getElementById('chatStream').checked;
            const temperature = parseFloat(document.getElementById('chatTemp').value);
            const provider = document.getElementById('chatProvider').value;

            if (!input.value.trim()) return;

            button.disabled = true;
            appendMessage('chatOutput', input.value, true);

            try {
                console.log('Sending chat request:', {
                    messages: [{role: 'user', content: input.value}],
                    stream: isStream,
                    temperature: temperature,
                    provider: provider
                });

                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': isStream ? 'text/event-stream' : 'application/json'
                    },
                    body: JSON.stringify({
                        messages: [{role: 'user', content: input.value}],
                        stream: isStream,
                        temperature: temperature,
                        provider: provider
                    })
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', Object.fromEntries(response.headers.entries()));

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                if (isStream) {
                    if (!response.body) {
                        throw new Error('No response body available for streaming');
                    }
                    await streamResponse(response, 'chatOutput');
                } else {
                    const data = await response.json();
                    console.log('Non-stream response:', data);
                    if (data.error) {
                        appendMessage('chatOutput', `错误: ${data.error}`, false);
                    } else if (data.choices && data.choices[0]) {
                        appendMessage('chatOutput', data.choices[0].content, false);
                    } else {
                        appendMessage('chatOutput', '返回数据格式错误', false);
                    }
                }
            } catch (error) {
                console.error('Chat error:', error);
                appendMessage('chatOutput', `错误: ${error.message}`, false);
            } finally {
                input.value = '';
                button.disabled = false;
            }
        }

        // Reasoning模型
        async function sendReasoning() {
            const input = document.getElementById('reasoningInput');
            const button = document.getElementById('reasoningButton');
            const isStream = document.getElementById('reasoningStream').checked;
            const temperature = parseFloat(document.getElementById('reasoningTemp').value);
            const provider = document.getElementById('reasoningProvider').value;

            if (!input.value.trim()) return;

            button.disabled = true;
            appendMessage('reasoningOutput', input.value, true);

            try {
                console.log('Sending reasoning request:', {
                    messages: [{role: 'user', content: input.value}],
                    stream: isStream,
                    temperature: temperature,
                    provider: provider
                });

                const response = await fetch('/api/reasoning', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': isStream ? 'text/event-stream' : 'application/json'
                    },
                    body: JSON.stringify({
                        messages: [{role: 'user', content: input.value}],
                        stream: isStream,
                        temperature: temperature,
                        provider: provider
                    })
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', Object.fromEntries(response.headers.entries()));

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                if (isStream) {
                    if (!response.body) {
                        throw new Error('No response body available for streaming');
                    }
                    await streamResponse(response, 'reasoningOutput');
                } else {
                    const data = await response.json();
                    console.log('Non-stream response:', data);
                    if (data.error) {
                        appendMessage('reasoningOutput', `错误: ${data.error}`, false);
                    } else if (data.choices && data.choices[0]) {
                        appendMessage('reasoningOutput', data.choices[0].content, false);
                    } else {
                        appendMessage('reasoningOutput', '返回数据格式错误', false);
                    }
                }
            } catch (error) {
                console.error('Reasoning error:', error);
                appendMessage('reasoningOutput', `错误: ${error.message}`, false);
            } finally {
                input.value = '';
                button.disabled = false;
            }
        }

        // 添加回车发送功能
        document.getElementById('chatInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendChat();
            }
        });

        document.getElementById('reasoningInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendReasoning();
            }
        });
    </script>
</body>
</html> 