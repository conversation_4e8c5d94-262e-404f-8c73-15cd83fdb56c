from app.models.wechat_scan_upload import WechatScanUpload
from app.models.database import SessionLocal
from sqlalchemy.exc import SQLAlchemyError
import uuid
import datetime

class WechatScanUploadDAO:
    """微信扫码上传图片数据访问对象"""

    @staticmethod
    def generate_scan_id():
        """生成唯一的扫码ID"""
        db = SessionLocal()
        try:
            # 生成UUID作为扫码ID
            while True:
                scan_id = str(uuid.uuid4()).replace('-', '')[:16]  # 取前16位，确保不会太长
                # 检查是否已存在
                existing = db.query(WechatScanUpload).filter(
                    WechatScanUpload.scan_id == scan_id
                ).first()
                if not existing:
                    break
            
            # 创建新记录
            wechat_scan = WechatScanUpload(
                scan_id=scan_id,
                image_base64=None,
                is_consumed=False
            )
            db.add(wechat_scan)
            db.commit()
            db.refresh(wechat_scan)
            return scan_id
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def bind_image_to_scan_id(scan_id, image_base64):
        """将图片绑定到扫码ID"""
        db = SessionLocal()
        try:
            # 查找对应的扫码记录
            wechat_scan = db.query(WechatScanUpload).filter(
                WechatScanUpload.scan_id == scan_id
            ).first()
            
            if not wechat_scan:
                return False, "扫码ID不存在"
            
            # 更新图片数据，重置消费状态
            wechat_scan.image_base64 = image_base64
            wechat_scan.is_consumed = False  # 重新绑定后可以再次查询
            wechat_scan.update_time = datetime.datetime.now()
            
            db.commit()
            return True, "图片绑定成功"
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def get_image_by_scan_id(scan_id):
        """通过扫码ID获取绑定的图片，查询后将记录标记为已消费"""
        db = SessionLocal()
        try:
            # 查找对应的扫码记录
            wechat_scan = db.query(WechatScanUpload).filter(
                WechatScanUpload.scan_id == scan_id,
                WechatScanUpload.is_consumed == False  # 只查询未消费的记录
            ).first()
            
            if not wechat_scan or not wechat_scan.image_base64:
                return None, "未找到绑定的图片或图片已被查询过"
            
            # 获取图片数据
            image_base64 = wechat_scan.image_base64
            
            # 标记为已消费
            wechat_scan.is_consumed = True
            wechat_scan.update_time = datetime.datetime.now()
            
            db.commit()
            return image_base64, "查询成功"
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def get_scan_record_by_id(scan_id):
        """根据扫码ID获取记录详情（不修改消费状态）"""
        db = SessionLocal()
        try:
            wechat_scan = db.query(WechatScanUpload).filter(
                WechatScanUpload.scan_id == scan_id
            ).first()
            
            if wechat_scan:
                return wechat_scan.to_dict()
            return None
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def clean_expired_records(hours=24):
        """清理过期的扫码记录（默认24小时）"""
        db = SessionLocal()
        try:
            cutoff_time = datetime.datetime.now() - datetime.timedelta(hours=hours)
            deleted_count = db.query(WechatScanUpload).filter(
                WechatScanUpload.create_time < cutoff_time
            ).delete()
            db.commit()
            return deleted_count
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close() 