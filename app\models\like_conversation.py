from sqlalchemy import Column, Integer, String, Text, DateTime
from sqlalchemy.ext.declarative import declarative_base
from app.models.conversation import Base
import datetime
import json

class LikeConversation(Base):
    """点赞对话记录模型"""
    __tablename__ = 'like_conversation'

    id = Column(Integer, primary_key=True, autoincrement=True)
    conversation_id = Column(Integer, nullable=False, index=True)  # 对话ID
    messages = Column(Text, nullable=False)  # 存储JSON格式的消息列表
    create_time = Column(DateTime, default=datetime.datetime.now)

    def to_dict(self):
        """将模型转换为字典"""
        return {
            'id': self.id,
            'conversationId': self.conversation_id,
            'messages': json.loads(self.messages),
            'createTime': self.create_time.strftime('%Y-%m-%d %H:%M:%S')
        }

    @staticmethod
    def from_dict(data):
        """从字典创建模型实例"""
        like_conversation = LikeConversation(
            conversation_id=data.get('conversationId'),
            messages=json.dumps(data.get('messages', []), ensure_ascii=False)  # 设置ensure_ascii=False以保持中文字符
        )
        if 'id' in data:
            like_conversation.id = data['id']
        return like_conversation 