from flask import Blueprint, request, jsonify
from flask_restx import Api, Resource, Namespace, fields
from app.models.conversation_dao import ConversationDAO
import json

# 创建命名空间
conversation_ns = Namespace('conversations', description='对话记录管理接口')

# 定义消息模型
message_model = conversation_ns.model('Message', {
    'role': fields.String(required=True, description='消息类型 (assistant/user)'),
    'content': fields.String(required=True, description='消息内容'),
    'reasoning': fields.String(required=False, description='推理过程')
})

# 定义对话记录模型
conversation_model = conversation_ns.model('Conversation', {
    'id': fields.Integer(readonly=True, description='对话记录ID'),
    'uid': fields.String(required=True, description='用户唯一标识'),
    'title': fields.String(required=True, description='对话标题'),
    'createTime': fields.String(readonly=True, description='创建时间'),
    'updateTime': fields.String(readonly=True, description='最后更新时间'),
    'messages': fields.List(fields.Nested(message_model), required=True, description='消息列表')
})

# 定义响应模型
response_model = conversation_ns.model('Response', {
    'code': fields.Integer(required=True, description='响应代码'),
    'message': fields.String(required=True, description='响应消息'),
    'data': fields.Raw(description='响应数据')
})

@conversation_ns.route('')
class ConversationList(Resource):
    @conversation_ns.doc('获取用户的所有对话记录')
    @conversation_ns.param('uid', '用户唯一标识', required=True)
    @conversation_ns.marshal_with(response_model)
    def get(self):
        """获取用户的所有对话记录"""
        try:
            uid = request.args.get('uid')
            if not uid:
                return {
                    'code': 400,
                    'message': '缺少必要参数: uid',
                    'data': None
                }, 400
            
            conversations = ConversationDAO.get_conversations_by_uid(uid)
            return {
                'code': 200,
                'message': '获取对话记录成功',
                'data': conversations
            }
        except Exception as e:
            return {
                'code': 500,
                'message': f'获取对话记录失败: {str(e)}',
                'data': None
            }, 500

    @conversation_ns.doc('创建新的对话记录')
    @conversation_ns.expect(conversation_model)
    @conversation_ns.marshal_with(response_model)
    def post(self):
        """创建新的对话记录"""
        try:
            data = request.get_json()
            if not data:
                return {
                    'code': 400,
                    'message': '无效的请求数据',
                    'data': None
                }, 400
            
            # 验证必要字段
            if not all(key in data for key in ['uid', 'title', 'messages']):
                return {
                    'code': 400,
                    'message': '缺少必要字段: uid, title, messages',
                    'data': None
                }, 400
            
            # 确保messages是JSON字符串
            if isinstance(data['messages'], list):
                data['messages'] = json.dumps(data['messages'], ensure_ascii=False)
            
            conversation = ConversationDAO.create_conversation(data)
            return {
                'code': 201,
                'message': '创建对话记录成功',
                'data': conversation
            }, 201
        except Exception as e:
            return {
                'code': 500,
                'message': f'创建对话记录失败: {str(e)}',
                'data': None
            }, 500

@conversation_ns.route('/<int:conversation_id>')
@conversation_ns.param('conversation_id', '对话记录ID')
class ConversationItem(Resource):
    @conversation_ns.doc('获取指定对话记录')
    @conversation_ns.param('uid', '用户唯一标识', required=True)
    @conversation_ns.marshal_with(response_model)
    def get(self, conversation_id):
        """获取指定ID的对话记录"""
        try:
            uid = request.args.get('uid')
            if not uid:
                return {
                    'code': 400,
                    'message': '缺少必要参数: uid',
                    'data': None
                }, 400
            
            conversation = ConversationDAO.get_conversation_by_id(conversation_id, uid)
            if conversation:
                return {
                    'code': 200,
                    'message': '获取对话记录成功',
                    'data': conversation
                }
            return {
                'code': 404,
                'message': f'对话记录不存在: ID {conversation_id}',
                'data': None
            }, 404
        except Exception as e:
            return {
                'code': 500,
                'message': f'获取对话记录失败: {str(e)}',
                'data': None
            }, 500

    @conversation_ns.doc('更新对话记录')
    @conversation_ns.param('uid', '用户唯一标识', required=True)
    @conversation_ns.expect(conversation_model)
    @conversation_ns.marshal_with(response_model)
    def put(self, conversation_id):
        """更新指定ID的对话记录"""
        try:
            uid = request.args.get('uid')
            if not uid:
                return {
                    'code': 400,
                    'message': '缺少必要参数: uid',
                    'data': None
                }, 400
            
            data = request.get_json()
            if not data:
                return {
                    'code': 400,
                    'message': '无效的请求数据',
                    'data': None
                }, 400
            
            # 确保messages是JSON字符串
            if 'messages' in data and isinstance(data['messages'], list):
                data['messages'] = json.dumps(data['messages'], ensure_ascii=False)
            
            conversation = ConversationDAO.update_conversation(conversation_id, data, uid)
            if conversation:
                return {
                    'code': 200,
                    'message': '更新对话记录成功',
                    'data': conversation
                }
            return {
                'code': 404,
                'message': f'对话记录不存在: ID {conversation_id}',
                'data': None
            }, 404
        except Exception as e:
            return {
                'code': 500,
                'message': f'更新对话记录失败: {str(e)}',
                'data': None
            }, 500

    @conversation_ns.doc('删除对话记录')
    @conversation_ns.param('uid', '用户唯一标识', required=True)
    @conversation_ns.marshal_with(response_model)
    def delete(self, conversation_id):
        """删除指定ID的对话记录"""
        try:
            uid = request.args.get('uid')
            if not uid:
                return {
                    'code': 400,
                    'message': '缺少必要参数: uid',
                    'data': None
                }, 400
            
            result = ConversationDAO.delete_conversation(conversation_id, uid)
            if result:
                return {
                    'code': 200,
                    'message': '删除对话记录成功',
                    'data': None
                }
            return {
                'code': 404,
                'message': f'对话记录不存在: ID {conversation_id}',
                'data': None
            }, 404
        except Exception as e:
            return {
                'code': 500,
                'message': f'删除对话记录失败: {str(e)}',
                'data': None
            }, 500 