from flask_restx import Namespace, Resource, fields
from flask import request, jsonify
import logging
from app.models.wechat_scan_upload_dao import WechatScanUploadDAO

# 创建命名空间
wechat_scan_ns = Namespace('微信扫码', description='微信扫码上传图片相关接口')

# 配置日志
logger = logging.getLogger(__name__)

# 定义扫码ID生成响应模型
scan_id_response = wechat_scan_ns.model('ScanIdResponse', {
    'code': fields.Integer(description='响应状态码'),
    'msg': fields.String(description='响应消息'),
    'data': fields.Raw(description='扫码ID数据')
})

# 定义图片绑定请求模型
bind_image_request = wechat_scan_ns.model('BindImageRequest', {
    'scanId': fields.String(required=True, description='扫码ID'),
    'imageBase64': fields.String(required=True, description='图片的base64编码数据')
})

# 定义图片绑定响应模型
bind_image_response = wechat_scan_ns.model('BindImageResponse', {
    'code': fields.Integer(description='响应状态码'),
    'msg': fields.String(description='响应消息'),
    'data': fields.Raw(description='绑定结果数据')
})

# 定义图片查询响应模型
query_image_response = wechat_scan_ns.model('QueryImageResponse', {
    'code': fields.Integer(description='响应状态码'),
    'msg': fields.String(description='响应消息'),
    'data': fields.Raw(description='图片数据')
})

@wechat_scan_ns.route('/generate-scan-id')
class GenerateScanId(Resource):
    @wechat_scan_ns.doc('生成扫码ID')
    @wechat_scan_ns.response(200, '成功', scan_id_response)
    @wechat_scan_ns.response(500, '服务器内部错误')
    def post(self):
        """
        生成扫码ID接口
        
        功能说明：
        - 生成唯一的扫码ID
        - 扫码ID不会重复
        - 返回格式化的响应数据
        
        返回数据结构：
        {
            "code": 200,
            "msg": "生成扫码ID成功",
            "data": {
                "scanId": "生成的扫码ID"
            }
        }
        """
        try:
            # 生成扫码ID
            scan_id = WechatScanUploadDAO.generate_scan_id()
            
            logger.info(f"生成扫码ID成功: {scan_id}")
            
            return {
                'code': 200,
                'msg': '生成扫码ID成功',
                'data': {
                    'scanId': scan_id
                }
            }
            
        except Exception as e:
            logger.error(f"生成扫码ID失败: {str(e)}")
            return {
                'code': 500,
                'msg': f'生成扫码ID失败: {str(e)}',
                'data': None
            }, 500

@wechat_scan_ns.route('/bind-image')
class BindImage(Resource):
    @wechat_scan_ns.doc('绑定图片到扫码ID')
    @wechat_scan_ns.expect(bind_image_request)
    @wechat_scan_ns.response(200, '成功', bind_image_response)
    @wechat_scan_ns.response(400, '请求参数错误')
    @wechat_scan_ns.response(500, '服务器内部错误')
    def post(self):
        """
        扫码ID与上传图片绑定接口
        
        功能说明：
        - 将一张图片（base64格式）绑定到指定的扫码ID
        - 如果扫码ID不存在，返回错误
        - 如果用同样的扫码ID再次绑定图片，会覆盖之前的图片
        - 重新绑定后可以再次查询到新图片
        
        请求参数：
        - scanId: 扫码ID（必填）
        - imageBase64: 图片的base64编码数据（必填）
        
        返回数据结构：
        {
            "code": 200,
            "msg": "图片绑定成功",
            "data": null
        }
        """
        try:
            data = request.get_json()
            if not data:
                return {
                    'code': 400,
                    'msg': '无效的请求数据',
                    'data': None
                }, 400
            
            scan_id = data.get('scanId')
            image_base64 = data.get('imageBase64')
            
            # 验证必要参数
            if not scan_id:
                return {
                    'code': 400,
                    'msg': '扫码ID不能为空',
                    'data': None
                }, 400
            
            if not image_base64:
                return {
                    'code': 400,
                    'msg': '图片数据不能为空',
                    'data': None
                }, 400
            
            # 绑定图片到扫码ID
            success, message = WechatScanUploadDAO.bind_image_to_scan_id(scan_id, image_base64)
            
            if success:
                logger.info(f"图片绑定成功: {scan_id}")
                return {
                    'code': 200,
                    'msg': message,
                    'data': None
                }
            else:
                logger.warning(f"图片绑定失败: {scan_id} - {message}")
                return {
                    'code': 400,
                    'msg': message,
                    'data': None
                }, 400
            
        except Exception as e:
            logger.error(f"绑定图片接口错误: {str(e)}")
            return {
                'code': 500,
                'msg': f'绑定图片失败: {str(e)}',
                'data': None
            }, 500

@wechat_scan_ns.route('/query-image/<string:scan_id>')
class QueryImage(Resource):
    @wechat_scan_ns.doc('通过扫码ID查询绑定的图片')
    @wechat_scan_ns.response(200, '成功', query_image_response)
    @wechat_scan_ns.response(404, '未找到图片')
    @wechat_scan_ns.response(500, '服务器内部错误')
    def get(self, scan_id):
        """
        通过扫码ID查询绑定图片接口
        
        功能说明：
        - 通过扫码ID查询绑定的图片
        - 如果查到图片，返回图片的base64数据
        - 一旦查到，再次用同样的扫码ID查询将返回null（一次性消费）
        - 如果用同样的扫码ID重新绑定图片，可以再次查到新图片
        
        路径参数：
        - scan_id: 扫码ID
        
        返回数据结构（成功）：
        {
            "code": 200,
            "msg": "查询成功",
            "data": {
                "imageBase64": "图片的base64数据"
            }
        }
        
        返回数据结构（未找到）：
        {
            "code": 200,
            "msg": "未找到绑定的图片或图片已被查询过",
            "data": null
        }
        """
        try:
            if not scan_id:
                return {
                    'code': 400,
                    'msg': '扫码ID不能为空',
                    'data': None
                }, 400
            
            # 查询绑定的图片
            image_base64, message = WechatScanUploadDAO.get_image_by_scan_id(scan_id)
            
            if image_base64:
                logger.info(f"查询图片成功: {scan_id}")
                return {
                    'code': 200,
                    'msg': message,
                    'data': {
                        'imageBase64': image_base64
                    }
                }
            else:
                logger.info(f"未找到图片: {scan_id} - {message}")
                return {
                    'code': 200,
                    'msg': message,
                    'data': None
                }
            
        except Exception as e:
            logger.error(f"查询图片接口错误: {str(e)}")
            return {
                'code': 500,
                'msg': f'查询图片失败: {str(e)}',
                'data': None
            }, 500

@wechat_scan_ns.route('/clean-expired')
class CleanExpiredRecords(Resource):
    @wechat_scan_ns.doc('清理过期的扫码记录')
    @wechat_scan_ns.response(200, '成功')
    @wechat_scan_ns.response(500, '服务器内部错误')
    def post(self):
        """
        清理过期的扫码记录接口（可选的维护接口）
        
        功能说明：
        - 清理超过24小时的扫码记录
        - 用于定期清理过期数据，释放存储空间
        
        返回数据结构：
        {
            "code": 200,
            "msg": "清理完成",
            "data": {
                "deletedCount": 清理的记录数量
            }
        }
        """
        try:
            deleted_count = WechatScanUploadDAO.clean_expired_records()
            
            logger.info(f"清理过期记录完成，删除了 {deleted_count} 条记录")
            
            return {
                'code': 200,
                'msg': '清理完成',
                'data': {
                    'deletedCount': deleted_count
                }
            }
            
        except Exception as e:
            logger.error(f"清理过期记录失败: {str(e)}")
            return {
                'code': 500,
                'msg': f'清理失败: {str(e)}',
                'data': None
            }, 500 