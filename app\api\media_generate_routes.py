from flask import Blueprint, request, jsonify
from flask_restx import Namespace, Resource, fields
import requests
import logging
from app.config import Config
from app.models.log_generate_image_dao import LogGenerateImageDAO
from app.models.log_generate_video_dao import LogGenerateVideoDAO

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建命名空间
media_ns = Namespace('media', description='图片和视频生成相关接口')

# 定义图片生成请求模型
image_generation_model = media_ns.model('ImageGeneration', {
    'uid': fields.String(required=True, description='用户唯一标识'),
    'prompt': fields.String(required=True, description='生成图片的提示词'),
    'negative_prompt': fields.String(description='负面提示词', default=''),
    'image_size': fields.String(description='图片尺寸', default='1024x1024'),
    'batch_size': fields.Integer(description='生成图片数量', default=1),
    'seed': fields.Integer(description='随机种子', default=4999999999),
    'num_inference_steps': fields.Integer(description='推理步数', default=20),
    'guidance_scale': fields.Float(description='引导系数', default=7.5),
    'model': fields.String(description='使用的模型', default='Kwai-Kolors/Kolors')
})

# 定义视频生成请求模型
video_generation_model = media_ns.model('VideoGeneration', {
    'uid': fields.String(required=True, description='用户唯一标识'),
    'prompt': fields.String(required=True, description='生成视频的提示词'),
    'seed': fields.Integer(description='随机种子', default=123),
    'model': fields.String(description='使用的模型', default='tencent/HunyuanVideo')
})

# 定义视频状态请求模型
video_status_model = media_ns.model('VideoStatus', {
    'requestId': fields.String(required=True, description='任务ID')
})

@media_ns.route('/generate/image')
class ImageGeneration(Resource):
    @media_ns.doc('生成图片')
    @media_ns.expect(image_generation_model)
    @media_ns.response(200, '成功')
    @media_ns.response(500, '服务器内部错误')
    def post(self):
        """生成AI图片"""
        try:
            data = request.get_json()
            if not data:
                logger.error("请求数据为空")
                return {
                    'code': 400,
                    'message': '无效的请求数据',
                    'data': None
                }, 400
            
            if 'prompt' not in data or 'uid' not in data:
                return {
                    'code': 400,
                    'message': '缺少必要参数: prompt或uid',
                    'data': None
                }, 400
            
            # 设置默认值
            prompt = data.get('prompt', '')
            negative_prompt = data.get('negative_prompt', '')
            image_size = data.get('image_size', '1024x1024')
            batch_size = data.get('batch_size', 1)
            seed = data.get('seed', 4999999999)
            num_inference_steps = data.get('num_inference_steps', 20)
            guidance_scale = data.get('guidance_scale', 7.5)
            model = data.get('model', 'Kwai-Kolors/Kolors')

            url = "https://api.siliconflow.cn/v1/images/generations"
            
            payload = {
                "model": model,
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "image_size": image_size,
                "batch_size": batch_size,
                "seed": seed,
                "num_inference_steps": num_inference_steps,
                "guidance_scale": guidance_scale
            }
            
            headers = {
                "Authorization": f"Bearer {Config.SILICON_FLOW_API_KEY}",
                "Content-Type": "application/json"
            }

            logger.info(f"发送请求到图片生成API: {url}")
            response = requests.post(url, json=payload, headers=headers)
            
            if response.status_code != 200:
                logger.error(f"API请求失败: {response.status_code} - {response.text}")
                return {
                    'code': response.status_code,
                    'message': f"API请求失败: {response.status_code}",
                    'data': None
                }, response.status_code
            
            try:
                result = response.json()
                # 记录生成日志
                LogGenerateImageDAO.create_log({'uid': data.get('uid'), 'prompt': prompt, 'model': model})
                return {
                    'code': 200,
                    'message': '成功',
                    'data': result
                }
            except ValueError as e:
                logger.error(f"JSON解析错误: {str(e)} - 响应内容: {response.text}")
                return {
                    'code': 500,
                    'message': '响应解析失败',
                    'data': None
                }, 500

        except Exception as e:
            logger.error(f"图片生成接口错误: {str(e)}")
            return {
                'code': 500,
                'message': str(e),
                'data': None
            }, 500

@media_ns.route('/generate/video')
class VideoGeneration(Resource):
    @media_ns.doc('生成视频')
    @media_ns.expect(video_generation_model)
    @media_ns.response(200, '成功')
    @media_ns.response(500, '服务器内部错误')
    def post(self):
        """生成AI视频"""
        try:
            data = request.get_json()
            if not data:
                logger.error("请求数据为空")
                return {
                    'code': 400,
                    'message': '无效的请求数据',
                    'data': None
                }, 400
            
            if 'prompt' not in data or 'uid' not in data:
                return {
                    'code': 400,
                    'message': '缺少必要参数: prompt或uid',
                    'data': None
                }, 400
            
            # 检查用户今日是否已达到生成限制
            uid = data.get('uid')
            today_count = LogGenerateVideoDAO.get_today_count_by_uid(uid)
            if today_count > 0:
                return {
                    'code': 429,
                    'message': '今日额度已经用完',
                    'data': None
                }, 200
            
            # 设置默认值
            prompt = data.get('prompt', '')
            seed = data.get('seed', 123)
            model = data.get('model', 'Wan-AI/Wan2.1-T2V-14B-Turbo')

            url = "https://api.siliconflow.cn/v1/video/submit"
            
            payload = {
                "model": model,
                "prompt": prompt,
                "seed": seed
            }
            
            headers = {
                "Authorization": f"Bearer {Config.SILICON_FLOW_API_KEY}",
                "Content-Type": "application/json"
            }

            logger.info(f"发送请求到视频生成API: {url}")
            response = requests.post(url, json=payload, headers=headers)
            
            if response.status_code != 200:
                logger.error(f"API请求失败: {response.status_code} - {response.text}")
                return {
                    'code': response.status_code,
                    'message': f"API请求失败: {response.status_code}",
                    'data': None
                }, response.status_code
            
            try:
                result = response.json()
                # 记录生成日志
                LogGenerateVideoDAO.create_log({'uid': data.get('uid'), 'prompt': prompt, 'model': model})
                return {
                    'code': 200,
                    'message': '成功',
                    'data': result
                }
            except ValueError as e:
                logger.error(f"JSON解析错误: {str(e)} - 响应内容: {response.text}")
                return {
                    'code': 500,
                    'message': '响应解析失败',
                    'data': None
                }, 500

        except Exception as e:
            logger.error(f"视频生成接口错误: {str(e)}")
            return {
                'code': 500,
                'message': str(e),
                'data': None
            }, 500

@media_ns.route('/video/status')
class VideoGenerationStatus(Resource):
    @media_ns.doc('生成视频状态')
    @media_ns.expect(video_status_model)
    @media_ns.response(200, '成功')
    @media_ns.response(500, '服务器内部错误')
    def post(self):
        """生成AI视频状态"""
        try:
            data = request.get_json()
            if not data:
                logger.error("请求数据为空")
                return {
                    'code': 400,
                    'message': '无效的请求数据',
                    'data': None
                }, 400
            
            if 'requestId' not in data:
                return {
                    'code': 400,
                    'message': '缺少必要参数: requestId',
                    'data': None
                }, 400
            
            # 设置默认值
            requestId = data.get('requestId', '')

            url = "https://api.siliconflow.cn/v1/video/status"
            
            payload = {
                "requestId": requestId,
            }
            
            headers = {
                "Authorization": f"Bearer {Config.SILICON_FLOW_API_KEY}",
                "Content-Type": "application/json"
            }

            logger.info(f"视频生成状态查询API: {url}")
            response = requests.post(url, json=payload, headers=headers)
            
            if response.status_code != 200:
                logger.error(f"API请求失败: {response.status_code} - {response.text}")
                return {
                    'code': response.status_code,
                    'message': f"API请求失败: {response.status_code}",
                    'data': None
                }, response.status_code
            
            try:
                result = response.json()
                return {
                    'code': 200,
                    'message': '成功',
                    'data': result
                }
            except ValueError as e:
                logger.error(f"JSON解析错误: {str(e)} - 响应内容: {response.text}")
                return {
                    'code': 500,
                    'message': '响应解析失败',
                    'data': None
                }, 500

        except Exception as e:
            logger.error(f"视频生成状态查询接口错误: {str(e)}")
            return {
                'code': 500,
                'message': str(e),
                'data': None
            }, 500
