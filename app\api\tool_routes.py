from flask_restx import Namespace, Resource, fields
from flask import request, Response, jsonify, send_file, make_response, after_this_request
import logging
import yt_dlp
import tempfile
import os
import shutil
import platform
from datetime import datetime
from urllib.parse import urlparse
import re
import json
import subprocess
import threading
import time
import atexit
import stat
import requests
import paramiko
import base64
import uuid
from werkzeug.utils import secure_filename
from PIL import Image
import io
from app.env_config import get_cdn_config

# 创建命名空间
tool_ns = Namespace('工具', description='工具类API接口')

# 定义bilibili视频下载请求模型
bilibili_download_request = tool_ns.model('BilibiliDownloadRequest', {
    'video_url': fields.String(required=True, description='B站视频网址，例如：https://www.bilibili.com/video/BV1tV4115717')
})

# 定义链接内容获取请求模型
link_content_request = tool_ns.model('LinkContentRequest', {
    'message': fields.String(required=True, description='包含链接的消息内容')
})

# 定义链接内容获取响应模型
link_content_response = tool_ns.model('LinkContentResponse', {
    'content': fields.String(description='链接内容分析结果')
})

# 定义图片上传请求模型
image_upload_request = tool_ns.model('ImageUploadRequest', {
    'image_data': fields.String(description='图片的base64编码数据（可选，与file二选一）'),
    'filename': fields.String(description='图片文件名（当使用base64时必填）')
})

# 定义图片上传响应模型
image_upload_response = tool_ns.model('ImageUploadResponse', {
    'success': fields.Boolean(description='上传是否成功'),
    'message': fields.String(description='响应消息'),
    'cdn_url': fields.String(description='图片的CDN地址')
})

# 定义阿里云文件上传响应模型
aliyun_file_upload_response = tool_ns.model('AliyunFileUploadResponse', {
    'success': fields.Boolean(description='上传是否成功'),
    'message': fields.String(description='响应消息'),
    'file_id': fields.String(description='阿里云文件ID')
})

# 日志配置
logger = logging.getLogger("tool_routes")

# 检测操作系统类型
IS_WINDOWS = platform.system() == 'Windows'

# 视频文件缓存，避免重复下载
video_cache = {}

# 创建临时文件清理器
class TempFileCleaner:
    def __init__(self):
        self.temp_dirs = set()
        self.lock = threading.Lock()
        self.cleaner_thread = threading.Thread(target=self._cleanup_worker, daemon=True)
        self.cleaner_thread.start()
    
    def add_temp_dir(self, temp_dir):
        with self.lock:
            self.temp_dirs.add(temp_dir)
    
    def _rmtree_onerror(self, func, path, exc_info):
        """
        处理删除文件时的权限错误，特别是在Linux系统中
        """
        logger.debug(f"处理删除权限错误: {path}")
        # 检查是否是权限错误
        if not os.access(path, os.W_OK):
            # 添加写权限
            os.chmod(path, stat.S_IWUSR)
            # 重试删除
            func(path)
        else:
            raise
    
    def _cleanup_worker(self):
        while True:
            dirs_to_remove = set()
            with self.lock:
                # 复制当前目录集合，避免在循环中修改
                dirs_to_check = self.temp_dirs.copy()
            
            # 尝试删除每个目录
            for dir_path in dirs_to_check:
                try:
                    # 如果目录不存在，跳过并从集合中移除
                    if not os.path.exists(dir_path):
                        with self.lock:
                            self.temp_dirs.discard(dir_path)
                        continue
                    
                    # 尝试删除目录及其内容，使用onerror处理权限问题
                    shutil.rmtree(dir_path, onerror=self._rmtree_onerror)
                    dirs_to_remove.add(dir_path)
                    logger.info(f"成功清理临时目录: {dir_path}")
                except Exception as e:
                    # 如果删除失败，先记录错误，稍后再尝试
                    logger.debug(f"暂时无法清理临时目录 {dir_path}: {str(e)}")
            
            # 从集合中移除已成功删除的目录
            with self.lock:
                self.temp_dirs -= dirs_to_remove
            
            # 等待一段时间后再次尝试
            time.sleep(300)  # 每5分钟尝试清理一次
    
    def cleanup_all(self):
        with self.lock:
            dirs_to_check = self.temp_dirs.copy()
        
        for dir_path in dirs_to_check:
            try:
                if os.path.exists(dir_path):
                    shutil.rmtree(dir_path, onerror=self._rmtree_onerror)
                    logger.info(f"程序退出时清理临时目录: {dir_path}")
            except Exception as e:
                logger.error(f"清理临时目录失败: {dir_path}, 错误: {str(e)}")
        
        with self.lock:
            self.temp_dirs.clear()

# 创建临时文件清理器实例
temp_cleaner = TempFileCleaner()

# 注册程序退出时的清理函数
atexit.register(temp_cleaner.cleanup_all)

# 内部函数，不对外暴露
def is_ffmpeg_installed():
    """检查系统是否安装了ffmpeg"""
    try:
        # 不同系统上检查ffmpeg的命令
        if IS_WINDOWS:
            # Windows上检查
            result = subprocess.run(['where', 'ffmpeg'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            return result.returncode == 0
        else:
            # Linux/Mac上检查
            result = subprocess.run(['which', 'ffmpeg'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            return result.returncode == 0
    except (subprocess.SubprocessError, FileNotFoundError):
        # 如果命令失败，尝试直接运行ffmpeg
        try:
            subprocess.run(['ffmpeg', '-version'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            return True
        except (subprocess.SubprocessError, FileNotFoundError):
            return False

# 内部函数，不对外暴露
def get_video_info(video_url):
    """获取视频信息和可用格式"""
    ydl_opts = {
        'quiet': True,
        'no_warnings': True,
        'nocheckcertificate': True,
    }
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        info = ydl.extract_info(video_url, download=False)
        formats = []
        if 'formats' in info:
            for f in info['formats']:
                format_info = {
                    'format_id': f.get('format_id', 'unknown'),
                    'ext': f.get('ext', 'unknown'),
                    'resolution': f.get('resolution', 'unknown'),
                    'filesize': f.get('filesize', 'unknown'),
                    'vcodec': f.get('vcodec', 'unknown'),
                    'acodec': f.get('acodec', 'unknown'),
                }
                formats.append(format_info)
        
        return {
            'title': info.get('title', 'unknown'),
            'duration': info.get('duration', 0),
            'formats': formats,
            'best_format': info.get('format_id', 'unknown')
        }

# 内部函数，不对外暴露
def download_video(video_url, bv_id):
    """下载视频并返回文件路径"""
    # 检查缓存中是否已有此视频
    cache_key = f"{bv_id}_{int(time.time() / 3600)}"  # 按小时级别缓存
    if cache_key in video_cache and os.path.exists(video_cache[cache_key]['file_path']):
        logger.info(f"使用缓存的视频文件: {video_cache[cache_key]['file_path']}")
        return video_cache[cache_key]
    
    # 创建临时目录存储下载的视频
    temp_dir = tempfile.mkdtemp(prefix="bilibili_")
    # 设置临时目录的权限（对Linux尤其重要）
    if not IS_WINDOWS:
        os.chmod(temp_dir, stat.S_IRWXU | stat.S_IRWXG | stat.S_IROTH | stat.S_IXOTH)
    
    # 添加到清理器中
    temp_cleaner.add_temp_dir(temp_dir)
    
    # 检查是否安装了ffmpeg
    has_ffmpeg = is_ffmpeg_installed()
    logger.info(f"系统ffmpeg安装状态: {'已安装' if has_ffmpeg else '未安装'}")
    
    # 配置yt-dlp选项 - 优先考虑下载速度而非清晰度
    if has_ffmpeg:
        # 如果有ffmpeg，选择文件大小较小的格式，优先速度
        format_spec = 'worstvideo[height>=480]+worstaudio/worst[height>=480]'
    else:
        # 如果没有ffmpeg，选择单文件格式中速度较快的
        format_spec = 'worst[height>=480][ext=mp4]/worst[height>=480]'
    
    logger.info(f"使用格式规格: {format_spec}")
    
    # 确保文件名是安全的路径（替换非法字符）
    safe_bv_id = re.sub(r'[^\w\-_\.]', '_', bv_id)
    output_template = os.path.join(temp_dir, f"{safe_bv_id}.%(ext)s")
    
    # 配置yt-dlp选项
    ydl_opts = {
        'format': format_spec,
        'outtmpl': output_template,
        'quiet': True,
        'no_warnings': True,
        'nocheckcertificate': True,
    }
    
    # 如果有ffmpeg，添加合并选项
    if has_ffmpeg:
        ydl_opts['merge_output_format'] = 'mp4'
    
    # 下载视频
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        logger.info(f"开始下载视频: {video_url}")
        ydl.download([video_url])
    
    # 检查临时目录中的文件
    files = os.listdir(temp_dir)
    if not files:
        raise Exception("视频下载失败，无法获取视频文件")
    
    # 找到下载的文件
    downloaded_file = None
    for file in files:
        full_path = os.path.join(temp_dir, file)
        if os.path.isfile(full_path):
            downloaded_file = full_path
            # 确保文件有适当的权限
            if not IS_WINDOWS:
                os.chmod(full_path, stat.S_IRUSR | stat.S_IWUSR | stat.S_IRGRP | stat.S_IROTH)
            break
    
    if not downloaded_file:
        raise Exception("视频下载失败，无法找到文件")
    
    # 如果文件不是mp4格式，确定正确的MIME类型
    file_ext = os.path.splitext(downloaded_file)[1].lower()
    mime_type = "video/mp4"
    if file_ext != '.mp4':
        if file_ext == '.flv':
            mime_type = "video/x-flv"
        elif file_ext == '.webm':
            mime_type = "video/webm"
        elif file_ext == '.mkv':
            mime_type = "video/x-matroska"
    
    logger.info(f"视频下载成功: {downloaded_file}")
    
    # 将视频添加到缓存
    result = {
        'file_path': downloaded_file,
        'mime_type': mime_type,
        'file_ext': file_ext,
        'temp_dir': temp_dir
    }
    video_cache[cache_key] = result
    
    return result

@tool_ns.route('/bilibili_download')
class BilibiliDownloadAPI(Resource):
    @tool_ns.expect(bilibili_download_request)
    @tool_ns.response(200, 'Success')
    @tool_ns.response(400, 'Bad Request')
    @tool_ns.response(500, 'Internal Server Error')
    def post(self):
        """下载B站视频并返回mp4文件"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "无效的请求数据"}), 400
                
            video_url = data.get('video_url')
            
            # 验证必要参数
            if not video_url:
                return jsonify({"error": "视频网址不能为空"}), 400
                
            # 验证URL是否为bilibili网址
            parsed_url = urlparse(video_url)
            if not parsed_url.netloc.endswith('bilibili.com'):
                return jsonify({"error": "提供的URL不是有效的B站视频链接"}), 400
                
            # 提取BV号，用于文件命名
            bv_match = re.search(r'BV\w+', video_url)
            bv_id = bv_match.group(0) if bv_match else 'video'
            
            try:
                # 下载视频或获取缓存
                video_info = download_video(video_url, bv_id)
                downloaded_file = video_info['file_path']
                mime_type = video_info['mime_type']
                file_ext = video_info['file_ext']
                
                try:
                    # 确保下载名称安全
                    safe_bv_id = re.sub(r'[^\w\-_\.]', '_', bv_id)
                    download_name = f"{safe_bv_id}{file_ext}"
                    
                    # 检查文件是否可读
                    if not os.access(downloaded_file, os.R_OK):
                        logger.warning(f"文件权限不足: {downloaded_file}")
                        if not IS_WINDOWS:
                            os.chmod(downloaded_file, stat.S_IRUSR | stat.S_IWUSR | stat.S_IRGRP | stat.S_IROTH)
                    
                    # 发送视频文件
                    response = make_response(send_file(
                        downloaded_file,
                        mimetype=mime_type,
                        as_attachment=True,
                        download_name=download_name,
                        conditional=True  # 支持断点续传
                    ))
                    
                    # 添加额外的响应头以优化文件传输
                    response.headers['Accept-Ranges'] = 'bytes'
                    response.headers['Cache-Control'] = 'no-cache'
                    
                    return response
                    
                except Exception as e:
                    logger.error(f"发送文件时出错: {str(e)}")
                    # 如果发送文件失败，返回错误信息
                    return jsonify({"error": f"无法发送视频文件: {str(e)}"}), 500
                
            except Exception as e:
                logger.error(f"下载视频时出错: {str(e)}")
                error_message = str(e)
                
                # 检查是否是ffmpeg错误
                if "ffmpeg is not installed" in error_message:
                    return jsonify({
                        "error": "下载失败：系统未安装ffmpeg，无法合并视频和音频",
                        "solution": "请在系统中安装ffmpeg: apt-get install ffmpeg"
                    }), 500
                
                return jsonify({"error": f"下载视频失败: {error_message}"}), 500
                
        except Exception as e:
            logger.error(f"Bilibili下载API错误: {str(e)}")
            return jsonify({"error": str(e)}), 500

def chat_with_coze(message, user_id="123456789", conversation_id=None):
    base_url = "https://api.coze.cn/v3/chat"
    
    # 如果提供了conversation_id，则添加到URL
    if conversation_id:
        url = f"{base_url}?conversation_id={conversation_id}"
    else:
        url = base_url
    
    headers = {
        "Authorization": "Bearer pat_7N6ah786j9B963jKQrRWJuoL8cXVUaMhpoxZdXscayOmElvdbe5v0IPVSMluph3X",
        "Content-Type": "application/json"
    }
    
    payload = {
        "bot_id": "7505962869130133514",
        "user_id": user_id,
        "stream": True,
        "auto_save_history": False,
        "additional_messages": [
            {
                "role": "user",
                "content": message,
                "content_type": "text"
            }
        ]
    }
    
    try:
        # 使用stream=True参数来获取流式响应
        response = requests.post(url, headers=headers, data=json.dumps(payload), stream=True)
        response.raise_for_status()  # 检查HTTP错误
        
        # 处理流式响应
        buffer = ""
        current_event = None
        current_data = ""
        
        for chunk in response.iter_lines():
            if not chunk:
                continue
                
            # 将字节转换为字符串
            line = chunk.decode('utf-8')
            
            # 解析SSE格式 (event: xxx\ndata: xxx)
            if line.startswith('event:'):
                # 处理上一个完整事件（如果存在）
                if current_event and current_data:
                    result = process_event(current_event, current_data)
                    if result:
                        buffer += result
                
                # 开始新事件
                current_event = line[6:].strip()
                current_data = ""
            elif line.startswith('data:'):
                current_data = line[5:].strip()
        
        # 处理最后一个事件
        if current_event and current_data:
            result = process_event(current_event, current_data)
            if result:
                buffer += result
                
        return buffer.strip()  # 返回完整响应，并去除首尾空白字符
    except requests.exceptions.RequestException as e:
        logging.error(f"请求错误: {e}")
        return {"error": str(e)}

def process_event(event_type, data):
    """
    处理SSE事件
    
    Args:
        event_type (str): 事件类型
        data (str): 事件数据
        
    Returns:
        str: 提取的内容片段
    """
    logging.info(f"事件: {event_type}")
    
    # 处理[DONE]事件
    if event_type == "done" and data == '"[DONE]"':
        logging.info("[响应完成]")
        return ""
    
    try:
        # 解析JSON数据
        if data and data != '"[DONE]"':
            json_data = json.loads(data)
            
            # 主要处理消息增量事件
            if event_type == "conversation.message.delta":
                content = json_data.get("content", "")
                if content:
                    return content
            
            # 处理完成事件
            elif event_type == "conversation.message.completed":
                if "content" in json_data and json_data.get("type") == "answer":
                    full_content = json_data.get("content", "")
                    logging.info(f"完整消息: {full_content}")
                    
            # 调试其他事件类型
            else:
                if event_type not in ["conversation.chat.created", "conversation.chat.in_progress"]:
                    logging.info(f"{event_type}: {json.dumps(json_data, ensure_ascii=False)}")
    
    except json.JSONDecodeError:
        logging.error(f"无法解析JSON: {data}")
    
    return ""

@tool_ns.route('/getLinkContent')
class LinkContentAPI(Resource):
    @tool_ns.expect(link_content_request)
    @tool_ns.response(200, 'Success', link_content_response)
    @tool_ns.response(400, 'Bad Request')
    @tool_ns.response(500, 'Internal Server Error')
    def post(self):
        """
        链接内容获取接口
        
        请求参数:
        - message: 用户发送的包含链接的消息内容
          
        返回内容:
        - content: 链接内容分析结果
        """
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "无效的请求数据"}), 400
                
            message = data.get('message')
            
            # 验证必要参数
            if not message:
                return jsonify({"error": "消息内容不能为空"}), 400
            
            # 调用coze聊天方法，用户ID固定为"tiye"，不传conversation_id
            try:
                response = chat_with_coze(message=message, user_id="tiye")
                
                # 检查是否有错误
                if isinstance(response, dict) and "error" in response:
                    return jsonify({"error": response["error"]}), 500
                
                # 确保返回值是正确的JSON格式
                return jsonify({"content": response})
                
            except Exception as e:
                logging.error(f"链接内容获取错误: {str(e)}")
                return jsonify({"error": f"链接内容获取失败: {str(e)}"}), 500
                
        except Exception as e:
            logging.error(f"链接内容获取API错误: {str(e)}")
            return jsonify({"error": str(e)}), 500

# CDN服务器配置从环境配置中获取

def validate_image_format(image_data):
    """验证图片格式并返回格式信息"""
    try:
        # 如果是文件对象，直接使用
        if hasattr(image_data, 'read'):
            image = Image.open(image_data)
        else:
            # 如果是字节数据，转换为BytesIO
            image = Image.open(io.BytesIO(image_data))
        
        # 验证是否为支持的图片格式
        if image.format not in ['JPEG', 'PNG', 'GIF', 'BMP', 'WEBP']:
            return False, None, "不支持的图片格式"
        
        return True, image.format.lower(), None
    except Exception as e:
        return False, None, f"图片格式验证失败: {str(e)}"

def generate_unique_filename(original_filename):
    """生成唯一的文件名"""
    # 获取文件扩展名
    if '.' in original_filename:
        name, ext = os.path.splitext(original_filename)
        ext = ext.lower()
    else:
        name = original_filename
        ext = '.png'  # 默认扩展名
    
    # 生成唯一标识符
    unique_id = str(uuid.uuid4())[:8]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 组合新文件名
    new_filename = f"{name}_{timestamp}_{unique_id}{ext}"
    return secure_filename(new_filename)

def upload_to_cdn_server(local_file_path, remote_filename):
    """上传文件到CDN服务器"""
    ssh_client = None
    sftp_client = None
    
    try:
        # 获取当前环境的CDN配置
        cdn_config = get_cdn_config()
        
        # 创建SSH连接
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        ssh_client.connect(
            hostname=cdn_config['ssh']['host'],
            port=cdn_config['ssh']['port'],
            username=cdn_config['ssh']['username'],
            password=cdn_config['ssh']['password'],
            timeout=30
        )
        
        # 创建SFTP客户端
        sftp_client = ssh_client.open_sftp()
        
        # 确保远程目录存在
        remote_dir = cdn_config['remote_path']
        try:
            sftp_client.stat(remote_dir)
        except FileNotFoundError:
            # 如果目录不存在，创建目录
            ssh_client.exec_command(f"mkdir -p {remote_dir}")
        
        # 上传文件
        remote_file_path = f"{remote_dir}/{remote_filename}"
        sftp_client.put(local_file_path, remote_file_path)
        
        # 设置文件权限
        sftp_client.chmod(remote_file_path, 0o644)
        
        # 生成CDN URL
        cdn_url = f"{cdn_config['cdn_base_url']}/{remote_filename}"
        
        logger.info(f"文件上传成功: {cdn_url}")
        return True, cdn_url, None
        
    except Exception as e:
        logger.error(f"上传到CDN服务器失败: {str(e)}")
        return False, None, str(e)
    
    finally:
        # 清理连接
        if sftp_client:
            sftp_client.close()
        if ssh_client:
            ssh_client.close()

def process_image_upload(image_data, filename, is_base64=False):
    """处理图片上传的核心逻辑"""
    temp_file_path = None
    
    try:
        # 如果是base64数据，先解码
        if is_base64:
            try:
                # 移除base64前缀（如果存在）
                if ',' in image_data:
                    image_data = image_data.split(',')[1]
                
                # 解码base64数据
                image_bytes = base64.b64decode(image_data)
            except Exception as e:
                return False, None, f"base64解码失败: {str(e)}"
        else:
            # 如果是文件对象，读取数据
            image_bytes = image_data.read()
            image_data.seek(0)  # 重置文件指针
        
        # 验证图片格式
        is_valid, image_format, error_msg = validate_image_format(image_bytes)
        if not is_valid:
            return False, None, error_msg
        
        # 生成唯一文件名
        if not filename:
            filename = f"image.{image_format}"
        
        unique_filename = generate_unique_filename(filename)
        
        # 创建临时文件
        temp_file_path = tempfile.mktemp(suffix=f".{image_format}")
        
        # 写入临时文件
        with open(temp_file_path, 'wb') as temp_file:
            temp_file.write(image_bytes)
        
        # 上传到CDN服务器
        success, cdn_url, upload_error = upload_to_cdn_server(temp_file_path, unique_filename)
        
        if success:
            return True, cdn_url, None
        else:
            return False, None, upload_error
            
    except Exception as e:
        logger.error(f"处理图片上传时出错: {str(e)}")
        return False, None, str(e)
    
    finally:
        # 清理临时文件
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
            except Exception as e:
                logger.warning(f"清理临时文件失败: {str(e)}")

@tool_ns.route('/upload_image')
class UploadImageAPI(Resource):
    @tool_ns.expect(image_upload_request)
    @tool_ns.response(200, 'Success', image_upload_response)
    @tool_ns.response(400, 'Bad Request')
    @tool_ns.response(500, 'Internal Server Error')
    def post(self):
        """
        上传图片接口
        
        支持两种上传方式：
        1. 文件上传：通过form-data上传文件
        2. Base64上传：通过JSON传递base64编码的图片数据
        
        请求参数:
        - file: 图片文件（form-data方式）
        - image_data: 图片的base64编码数据（JSON方式，可选）
        - filename: 图片文件名（使用base64时必填）
          
        返回内容:
        - success: 上传是否成功
        - message: 响应消息
        - cdn_url: 图片的CDN地址
        """
        try:
            # 检查是否为文件上传
            if 'file' in request.files:
                file = request.files['file']
                
                if file.filename == '':
                    return jsonify({
                        "success": False,
                        "message": "没有选择文件"
                    }), 400
                
                # 处理文件上传
                success, cdn_url, error_msg = process_image_upload(
                    image_data=file,
                    filename=file.filename,
                    is_base64=False
                )
                
            else:
                # 检查是否为JSON数据上传
                data = request.get_json()
                if not data:
                    return jsonify({
                        "success": False,
                        "message": "请提供图片文件或base64数据"
                    }), 400
                
                image_data = data.get('image_data')
                filename = data.get('filename')
                
                if not image_data:
                    return jsonify({
                        "success": False,
                        "message": "image_data不能为空"
                    }), 400
                
                if not filename:
                    return jsonify({
                        "success": False,
                        "message": "使用base64上传时filename不能为空"
                    }), 400
                
                # 处理base64上传
                success, cdn_url, error_msg = process_image_upload(
                    image_data=image_data,
                    filename=filename,
                    is_base64=True
                )
            
            if success:
                return jsonify({
                    "success": True,
                    "message": "图片上传成功",
                    "cdn_url": cdn_url
                })
            else:
                return jsonify({
                    "success": False,
                    "message": f"图片上传失败: {error_msg}"
                }), 500
                
        except Exception as e:
            logger.error(f"上传图片API错误: {str(e)}")
            return jsonify({
                "success": False,
                "message": f"服务器内部错误: {str(e)}"
            }), 500

def upload_file_to_aliyun(file):
    """上传文件到阿里云并获取file_id"""
    try:
        # 从环境配置获取均衡AI服务器地址
        from app.env_config import BALANCE_AI_CONFIG
        
        # 根据当前环境获取服务器地址，默认使用development
        current_env = os.environ.get('FLASK_ENV', 'development')
        base_url = BALANCE_AI_CONFIG.get(current_env, BALANCE_AI_CONFIG['development'])['url']
        
        upload_url = f"{base_url}/junhengai/yxAgent/v2/upload/file"
        
        # 重置文件指针到开始位置
        file.seek(0)
        
        # 准备文件数据
        files = {'file': (file.filename, file, file.content_type)}
        
        # 发送上传请求
        response = requests.post(upload_url, files=files, timeout=30)
        response.raise_for_status()
        
        # 解析响应
        result = response.json()
        
        # 根据实际API响应格式解析file_id
        if result.get('code') == 200 and 'data' in result:
            file_id = result['data']  # data字段直接是file_id字符串
            logger.info(f"文件上传成功，获得file_id: {file_id}")
            return True, file_id, None
        else:
            logger.error(f"文件上传响应格式错误: {result}")
            return False, None, f"文件上传失败: {result.get('msg', '未知错误')}"
            
    except requests.exceptions.RequestException as e:
        logger.error(f"文件上传请求失败: {str(e)}")
        return False, None, f"文件上传请求失败: {str(e)}"
    except Exception as e:
        logger.error(f"文件上传过程中出错: {str(e)}")
        return False, None, f"文件上传过程中出错: {str(e)}"

def check_file_status(file_id, max_retries=30, retry_interval=5):
    """检查阿里云文件状态，支持轮询直到完成"""
    try:
        # 从环境配置获取均衡AI服务器地址
        from app.env_config import BALANCE_AI_CONFIG
        
        # 根据当前环境获取服务器地址，默认使用development
        current_env = os.environ.get('FLASK_ENV', 'development')
        base_url = BALANCE_AI_CONFIG.get(current_env, BALANCE_AI_CONFIG['development'])['url']
        
        status_url = f"{base_url}/junhengai/yxAgent/v2/file/status"
        
        for attempt in range(max_retries):
            try:
                # 发送状态查询请求
                params = {'fileSessionId': file_id}
                response = requests.get(status_url, params=params, timeout=30)
                response.raise_for_status()
                
                # 解析响应
                result = response.json()
                
                if 'data' in result:
                    status = result['data']
                    logger.info(f"文件状态查询 (第{attempt + 1}次): {status}")
                    
                    if status == "FINISH":
                        logger.info(f"文件处理完成: {file_id}")
                        return True, file_id, None
                    elif status == "EXPIRED":
                        logger.error(f"文件已过期: {file_id}")
                        return False, None, "文件已过期"
                    elif status == "ERROR":
                        logger.error(f"文件处理失败: {file_id}")
                        return False, None, "文件处理失败"
                    elif status == "PARSING":
                        # 如果还在解析中，继续等待
                        if attempt < max_retries - 1:
                            logger.info(f"文件正在解析中，{retry_interval}秒后重试...")
                            time.sleep(retry_interval)
                            continue
                        else:
                            logger.error(f"文件处理超时: {file_id}")
                            return False, None, "文件处理超时"
                    else:
                        logger.warning(f"未知的文件状态: {status}")
                        if attempt < max_retries - 1:
                            time.sleep(retry_interval)
                            continue
                        else:
                            return False, None, f"未知的文件状态: {status}"
                else:
                    logger.error(f"状态查询响应格式错误: {result}")
                    return False, None, "状态查询响应格式错误"
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"状态查询请求失败 (第{attempt + 1}次): {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(retry_interval)
                    continue
                else:
                    return False, None, f"状态查询请求失败: {str(e)}"
                    
    except Exception as e:
        logger.error(f"文件状态检查过程中出错: {str(e)}")
        return False, None, f"文件状态检查过程中出错: {str(e)}"

@tool_ns.route('/upload_aliyun_file')
class AliyunFileUploadAPI(Resource):
    @tool_ns.response(200, 'Success', aliyun_file_upload_response)
    @tool_ns.response(400, 'Bad Request')
    @tool_ns.response(500, 'Internal Server Error')
    def post(self):
        """
        上传文件到阿里云并获取文件ID
        
        请求参数:
        - file: 要上传的文件（form-data方式）
          
        返回内容:
        - success: 上传是否成功
        - message: 响应消息
        - file_id: 阿里云文件ID（仅在成功时返回）
        
        注意事项:
        - 该接口会先上传文件获取file_id
        - 然后轮询检查文件状态，直到状态为FINISH、EXPIRED或ERROR
        - 如果状态为PARSING，会每5秒轮询一次，最多尝试30次（2.5分钟）
        - 只有状态为FINISH的file_id才表示文件可用
        """
        try:
            # 检查是否有文件上传
            if 'file' not in request.files:
                return jsonify({
                    "success": False,
                    "message": "请提供要上传的文件"
                }), 400
                
            file = request.files['file']
            
            if file.filename == '':
                return jsonify({
                    "success": False,
                    "message": "没有选择文件"
                }), 400
            
            logger.info(f"开始上传文件到阿里云: {file.filename}")
            
            # 第一步：上传文件获取file_id
            upload_success, file_id, upload_error = upload_file_to_aliyun(file)
            
            if not upload_success:
                return jsonify({
                    "success": False,
                    "message": f"文件上传失败: {upload_error}"
                }), 500
            
            logger.info(f"文件上传成功，开始检查状态: {file_id}")
            
            # 第二步：检查文件状态
            status_success, final_file_id, status_error = check_file_status(file_id)
            
            if status_success:
                return jsonify({
                    "success": True,
                    "message": "文件上传并处理完成",
                    "file_id": final_file_id
                })
            else:
                return jsonify({
                    "success": False,
                    "message": f"文件状态检查失败: {status_error}"
                }), 500
                
        except Exception as e:
            logger.error(f"阿里云文件上传API错误: {str(e)}")
            return jsonify({
                "success": False,
                "message": f"服务器内部错误: {str(e)}"
            }), 500