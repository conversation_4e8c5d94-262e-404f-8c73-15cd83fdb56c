#!/bin/bash

echo "开始部署测试环境..."

# 构建新镜像
echo "构建测试环境镜像..."
docker build -t aihub-dev -f Dockerfile.dev . --no-cache

# 停止并删除旧容器
echo "停止旧容器..."
docker stop aihub-dev || true
docker rm aihub-dev || true

# 启动新容器
echo "启动测试环境容器..."
docker run -d \
    --name aihub-dev \
    -p 5000:5000 \
    -e FLASK_ENV=development \
    -e PORT=5000 \
    -v $(pwd)/logs:/app/logs \
    --restart unless-stopped \
    aihub-dev

echo "测试环境部署完成" 