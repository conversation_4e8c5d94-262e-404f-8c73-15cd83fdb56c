#!/usr/bin/env python3
"""测试阿里云百炼接口"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("正在测试导入...")
    
    # 测试基础导入
    from app.config import Config
    print("✓ Config导入成功")
    
    # 测试百炼配置
    bailian_config = Config.BAILIAN_CONFIG
    print(f"✓ 百炼配置: {bailian_config}")
    
    # 测试dashscope导入
    from dashscope import Application
    print("✓ dashscope导入成功")
    
    # 测试百炼模型导入
    from app.models.bailian_model import BailianModel
    print("✓ BailianModel导入成功")
    
    # 测试百炼模型初始化
    bailian = BailianModel()
    print(f"✓ BailianModel初始化成功，app_id: {bailian.app_id}")
    
    # 测试路由导入
    from app.api.bailian_routes import bailian_ns
    print("✓ bailian_routes导入成功")
    
    print("\n所有测试通过！阿里云百炼接口已成功集成。")
    
except Exception as e:
    print(f"❌ 测试失败: {str(e)}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
