from app.models.conversation import Conversation
from app.models.database import SessionLocal
from sqlalchemy.exc import SQLAlchemyError

class ConversationDAO:
    """对话记录数据访问对象"""

    @staticmethod
    def get_conversations_by_uid(uid):
        """获取指定用户的所有对话记录"""
        db = SessionLocal()
        try:
            conversations = db.query(Conversation).filter(Conversation.uid == uid).order_by(Conversation.create_time.desc()).all()
            return [conversation.to_dict() for conversation in conversations]
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def get_all_conversations():
        """获取所有对话记录"""
        db = SessionLocal()
        try:
            conversations = db.query(Conversation).order_by(Conversation.create_time.desc()).all()
            return [conversation.to_dict() for conversation in conversations]
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def get_conversation_by_id(conversation_id, uid=None):
        """根据ID获取对话记录"""
        db = SessionLocal()
        try:
            query = db.query(Conversation).filter(Conversation.id == conversation_id)
            if uid:
                query = query.filter(Conversation.uid == uid)
            conversation = query.first()
            if conversation:
                return conversation.to_dict()
            return None
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def create_conversation(conversation_data):
        """创建新的对话记录"""
        db = SessionLocal()
        try:
            conversation = Conversation.from_dict(conversation_data)
            db.add(conversation)
            db.commit()
            db.refresh(conversation)
            return conversation.to_dict()
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def update_conversation(conversation_id, conversation_data, uid=None):
        """更新对话记录"""
        db = SessionLocal()
        try:
            query = db.query(Conversation).filter(Conversation.id == conversation_id)
            if uid:
                query = query.filter(Conversation.uid == uid)
            conversation = query.first()
            if not conversation:
                return None
            
            # 更新字段
            if 'title' in conversation_data:
                conversation.title = conversation_data['title']
            if 'messages' in conversation_data:
                conversation.messages = conversation_data['messages']
            
            db.commit()
            db.refresh(conversation)
            return conversation.to_dict()
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def delete_conversation(conversation_id, uid=None):
        """删除对话记录"""
        db = SessionLocal()
        try:
            query = db.query(Conversation).filter(Conversation.id == conversation_id)
            if uid:
                query = query.filter(Conversation.uid == uid)
            conversation = query.first()
            if not conversation:
                return False
            
            db.delete(conversation)
            db.commit()
            return True
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close() 