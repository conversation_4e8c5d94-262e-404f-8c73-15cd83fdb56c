from openai import OpenAI
from typing import List, Dict, Any, Optional
import json
import logging
from app.config import Config

class DeepseekModel:
    """DeepSeek模型封装类"""
    
    def __init__(self, provider: str = None):
        model_config = Config.get_model_config(provider)
        self.client = OpenAI(
            api_key=model_config.api_key,
            base_url=model_config.api_base
        )
        self.model_config = model_config
        
    def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = None,
        stream: bool = False,
        temperature: float = 1.0,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """调用DeepSeek聊天补全API"""
        try:
            # 添加日志
            logging.info(f"开始调用模型 API, provider: {self.model_config.api_base}")
            
            model = model or self.model_config.chat_model
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                stream=stream,
                temperature=temperature,
                max_tokens=max_tokens,
                **kwargs
            )
            
            if stream:
                def generate():
                    try:
                        # 发送开始状态
                        yield json.dumps({
                            "status": 0,
                            "object": "chat.completion.chunk",
                            "model": model,
                            "choices": [{
                                "index": 0,
                                "content": "",
                                "finish_reason": None
                            }]
                        }, ensure_ascii=False)
                        
                        for chunk in response:
                            if hasattr(chunk.choices[0].delta, 'content') and chunk.choices[0].delta.content:
                                data = {
                                    "id": chunk.id,
                                    "object": "chat.completion.chunk",
                                    "created": chunk.created,
                                    "model": chunk.model,
                                    "status": 1,
                                    "choices": [{
                                        "index": chunk.choices[0].index,
                                        "content": chunk.choices[0].delta.content,
                                        "finish_reason": chunk.choices[0].finish_reason
                                    }]
                                }
                                yield json.dumps(data, ensure_ascii=False)
                            if hasattr(chunk.choices[0], 'finish_reason') and chunk.choices[0].finish_reason:
                                yield json.dumps({
                                    "status": 2,
                                    "object": "chat.completion.chunk",
                                    "model": chunk.model,
                                    "choices": [{
                                        "index": chunk.choices[0].index,
                                        "content": "",
                                        "finish_reason": chunk.choices[0].finish_reason
                                    }],
                                    "usage": {
                                        "prompt_tokens": chunk.usage.prompt_tokens,
                                        "completion_tokens": chunk.usage.completion_tokens,
                                        "total_tokens": chunk.usage.total_tokens
                                    }
                                }, ensure_ascii=False)
                    except Exception as e:
                        logging.error(f"Stream generation error: {str(e)}")
                        yield json.dumps({
                            "status": 2,
                            "object": "chat.completion.chunk",
                            "model": model,
                            "choices": [{
                                "index": 0,
                                "content": "",
                                "finish_reason": "error"
                            }],
                            "error": str(e)
                        }, ensure_ascii=False)
                return generate()
            
            return {
                "id": response.id,
                "object": "chat.completion",
                "created": response.created,
                "model": response.model,
                "status": 2,
                "choices": [{
                    "index": response.choices[0].index,
                    "content": response.choices[0].message.content,
                    "finish_reason": response.choices[0].finish_reason
                }],
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                }
            }
            
        except Exception as e:
            logging.error(f"DeepSeek API调用失败: {str(e)}, API Base: {self.model_config.api_base}")
            raise Exception(f"DeepSeek API调用失败: {str(e)}")

    def reasoning_completion(
        self,
        messages: List[Dict[str, str]],
        stream: bool = False,
        temperature: float = 1.0,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """调用DeepSeek推理模型API"""
        try:
            response = self.client.chat.completions.create(
                model=self.model_config.reasoner_model,
                messages=messages,
                stream=stream,
                temperature=temperature,
                max_tokens=max_tokens,
                **kwargs
            )
            
            if stream:
                def generate():
                    try:
                        # 发送开始状态
                        yield json.dumps({
                            "status": 0,
                            "object": "reasoning.completion.chunk",
                            "model": self.model_config.reasoner_model,
                            "choices": [{
                                "index": 0,
                                "content": "",
                                "reasoning_content": "",
                                "finish_reason": None
                            }]
                        }, ensure_ascii=False)
                        
                        for chunk in response:
                            if hasattr(chunk.choices[0].delta, 'content') or hasattr(chunk.choices[0].delta, 'reasoning_content'):
                                data = {
                                    "id": chunk.id,
                                    "object": "reasoning.completion.chunk",
                                    "created": chunk.created,
                                    "model": chunk.model,
                                    "status": 1,
                                    "choices": [{
                                        "index": chunk.choices[0].index,
                                        "content": chunk.choices[0].delta.content if hasattr(chunk.choices[0].delta, 'content') else "",
                                        "reasoning_content": chunk.choices[0].delta.reasoning_content if hasattr(chunk.choices[0].delta, 'reasoning_content') else "",
                                        "finish_reason": chunk.choices[0].finish_reason
                                    }]
                                }
                                yield json.dumps(data, ensure_ascii=False)
                            if hasattr(chunk.choices[0], 'finish_reason') and chunk.choices[0].finish_reason:
                                yield json.dumps({
                                    "status": 2,
                                    "object": "reasoning.completion.chunk",
                                    "model": chunk.model,
                                    "choices": [{
                                        "index": chunk.choices[0].index,
                                        "content": "",
                                        "reasoning_content": "",
                                        "finish_reason": chunk.choices[0].finish_reason
                                    }],
                                    "usage": {
                                        "prompt_tokens": chunk.usage.prompt_tokens,
                                        "completion_tokens": chunk.usage.completion_tokens,
                                        "total_tokens": chunk.usage.total_tokens
                                    }
                                }, ensure_ascii=False)
                    except Exception as e:
                        logging.error(f"Stream generation error: {str(e)}")
                        yield json.dumps({
                            "status": 2,
                            "object": "reasoning.completion.chunk",
                            "model": self.model_config.reasoner_model,
                            "choices": [{
                                "index": 0,
                                "content": "",
                                "finish_reason": "error"
                            }],
                            "error": str(e)
                        }, ensure_ascii=False)
                return generate()
            
            return {
                "id": response.id,
                "object": "reasoning.completion",
                "created": response.created,
                "model": response.model,
                "status": 2,
                "choices": [{
                    "index": response.choices[0].index,
                    "content": response.choices[0].message.content,
                    "finish_reason": response.choices[0].finish_reason
                }],
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                }
            }
            
        except Exception as e:
            logging.error(f"DeepSeek推理API调用失败: {str(e)}")
            raise Exception(f"DeepSeek推理API调用失败: {str(e)}")