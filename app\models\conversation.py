from sqlalchemy import Column, Integer, String, Text, DateTime
from sqlalchemy.ext.declarative import declarative_base
import datetime
import json

Base = declarative_base()

class Conversation(Base):
    """对话记录模型"""
    __tablename__ = 'conversations'

    id = Column(Integer, primary_key=True, autoincrement=True)
    uid = Column(String(50), nullable=False, index=True)  # 用户唯一标识
    title = Column(String(255), nullable=False)
    messages = Column(Text, nullable=False)  # 存储JSON格式的消息列表
    create_time = Column(DateTime, default=datetime.datetime.now)
    update_time = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    def to_dict(self):
        """将模型转换为字典"""
        return {
            'id': self.id,
            'uid': self.uid,
            'title': self.title,
            'messages': json.loads(self.messages),
            'createTime': self.create_time.strftime('%Y-%m-%d %H:%M:%S'),
            'updateTime': self.update_time.strftime('%Y-%m-%d %H:%M:%S')
        }

    @staticmethod
    def from_dict(data):
        """从字典创建模型实例"""
        conversation = Conversation(
            uid=data.get('uid'),
            title=data.get('title'),
            messages=json.dumps(data.get('messages', []), ensure_ascii=False)  # 设置ensure_ascii=False以保持中文字符
        )
        if 'id' in data:
            conversation.id = data['id']
        return conversation 