from app import create_app
from app.config import Config
from app.env_config import ENV
import logging

app = create_app()

if __name__ == "__main__":
    # 配置详细的日志
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logging.info(f"Current environment: {ENV}")
    logging.info(f"Starting server on http://{Config.HOST}:{Config.PORT}")
    logging.info(f"Debug mode: {Config.DEBUG}")
    
    app.run(
        host=Config.HOST,
        port=Config.PORT,
        debug=Config.DEBUG
    )