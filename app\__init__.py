from flask import Flask, send_from_directory, request, make_response, jsonify
from flask_restx import Api
from flask_cors import CORS
from .config import Config
from app.api.deepseek_routes import deepseek_ns
from app.api.conversation_routes import conversation_ns
from app.api.like_conversation_routes import like_conversation_ns
from app.api.dislike_conversation_routes import dislike_conversation_ns
from app.api.media_generate_routes import media_ns
from app.api.tool_routes import tool_ns
from app.api.wechat_scan_routes import wechat_scan_ns
from app.models.database import init_db
import logging
import os
import sqlalchemy.exc

def create_app():
    """创建Flask应用"""
    # 指定静态文件目录
    static_folder = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static')
    app = Flask(__name__, static_folder=static_folder)
    
    app.config.from_object(Config)
    # 添加CORS支持
    CORS(app)
    
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 初始化数据库
    try:
        init_db()
        logging.info("数据库初始化成功")
    except sqlalchemy.exc.OperationalError as e:
        logging.error(f"数据库连接错误: {str(e)}")
        logging.warning("应用程序将继续运行，但数据库功能可能不可用")
    
    # 初始化 API 文档
    api = Api(
        app,
        version='1.0',
        title='AI模型集成平台',
        description='REST API service for AI集成平台',
        doc='/docs'
    )
    
    # 注册命名空间
    api.add_namespace(deepseek_ns, path='/api')
    api.add_namespace(conversation_ns, path='/api/conversations')
    api.add_namespace(like_conversation_ns, path='/api/like-conversations')
    api.add_namespace(dislike_conversation_ns, path='/api/dislike-conversations')
    api.add_namespace(media_ns, path='/api/media')
    api.add_namespace(tool_ns, path='/api/tools')
    api.add_namespace(wechat_scan_ns, path='/api/wechat-scan')
    
    @app.route('/index')
    def index():
        try:
            return send_from_directory(static_folder, 'index.html')
        except Exception as e:
            logging.error(f"加载index.html失败: {str(e)}")
            return f"加载页面失败: {str(e)}", 500
    
    # 添加错误处理
    @app.errorhandler(500)
    def internal_error(error):
        logging.error(f"服务器内部错误: {str(error)}")
        return jsonify({"error": "服务器内部错误"}), 500
    
    @app.errorhandler(502)
    def bad_gateway(error):
        logging.error(f"网关错误: {str(error)}")
        return jsonify({"error": "网关错误"}), 502
    
    return app 