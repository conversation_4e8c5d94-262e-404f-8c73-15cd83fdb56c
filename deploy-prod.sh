#!/bin/bash

echo "开始部署生产环境..."

# 构建新镜像
echo "构建生产环境镜像..."
docker build -t aihub-prod -f Dockerfile.prod . --no-cache

# 停止并删除旧容器
echo "停止旧容器..."
docker stop aihub-prod || true
docker rm aihub-prod || true

# 启动新容器
echo "启动生产环境容器..."
docker run -d \
    --name aihub-prod \
    -p 5001:5001 \
    -e FLASK_ENV=production \
    -e PORT=5001 \
    -v $(pwd)/logs:/app/logs \
    --restart unless-stopped \
    aihub-prod

echo "生产环境部署完成" 