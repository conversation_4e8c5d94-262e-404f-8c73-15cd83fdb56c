from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.ext.declarative import declarative_base
from app.models.conversation import Base
from app.models.like_conversation import LikeConversation
from app.models.dislike_conversation import DislikeConversation
from app.models.dislike_conversation_config import DislikeConversationConfig
from app.models.log_generate_image import LogGenerateImage
from app.models.log_generate_video import LogGenerateVideo
from app.models.wechat_scan_upload import WechatScanUpload
from urllib.parse import quote_plus
from app.env_config import get_db_config

# 获取数据库配置
DB_CONFIG = get_db_config()

# 创建数据库连接URL（对密码进行URL编码）
DATABASE_URL = f"{DB_CONFIG['dialect']}+pymysql://{DB_CONFIG['username']}:{quote_plus(DB_CONFIG['password'])}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset=utf8mb4"

# 创建数据库引擎
engine = create_engine(
    DATABASE_URL,
    echo=False,
    pool_recycle=3600,
    # 设置连接字符集
    connect_args={'charset': 'utf8mb4'}
)

# 创建会话工厂
session_factory = sessionmaker(bind=engine)
SessionLocal = scoped_session(session_factory)

# 初始化数据库
def init_db():
    """初始化数据库，创建所有表"""
    Base.metadata.create_all(bind=engine)

# 获取数据库会话
def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close() 