import os
from dotenv import load_dotenv

load_dotenv()

# 获取环境变量
ENV = os.getenv('FLASK_ENV', 'development')

# 数据库配置
DATABASE_CONFIG = {
    'development': {
        'host': '**************',
        'port': 3306,
        'username': 'root',
        'password': 'Root@123',
        'dialect': 'mysql',
        'database': 'test_ai'
    },
    'production': {
        'host': '**************',
        'port': 3306,
        'username': 'root',
        'password': 'Root@123',
        'dialect': 'mysql',
        'database': 'prod_ai'
    }
}

# CDN服务器配置
CDN_CONFIG = {
    'development': {
        'ssh': {
            'host': '**************',
            'port': 22,
            'username': 'root',
            'password': 'lQKINgJYUQc^1sg@YwlQX5vc@KvD',
        },
        'remote_path': '/data/tiye_project/cdn/server_test',
        'cdn_base_url': 'https://junheng.chinatiye.cn/junheng_cdn/server_test'
    },
    'production': {
        'ssh': {
            'host': '**************',
            'port': 22,
            'username': 'root',
            'password': 'lQKINgJYUQc^1sg@YwlQX5vc@KvD',
        },
        'remote_path': '/data/tiye_project/cdn/server',
        'cdn_base_url': 'https://junheng.chinatiye.cn/junheng_cdn/server'
    }
}


# 均衡AI服务器
BALANCE_AI_CONFIG = {
    'development': {
        'url': 'https://junheng.chinatiye.cn',
    },
    'production': {
        'url': 'https://ai.chinatiye.cn',
    }
}

def get_db_config():
    """获取当前环境的数据库配置"""
    return DATABASE_CONFIG.get(ENV, DATABASE_CONFIG['development'])

def get_cdn_config():
    """获取当前环境的CDN配置"""
    return CDN_CONFIG.get(ENV, CDN_CONFIG['development']) 