from sqlalchemy import Column, Integer, String, Text, DateTime
from sqlalchemy.ext.declarative import declarative_base
from app.models.conversation import Base
import datetime

class DislikeConversationConfig(Base):
    """点踩配置项模型"""
    __tablename__ = 'dislike_conversation_config'

    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(50), nullable=False, unique=True)  # 错误代码
    name = Column(String(50), nullable=False)  # 错误名称
    create_time = Column(DateTime, default=datetime.datetime.now)
    update_time = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    def to_dict(self):
        """将模型转换为字典"""
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name
        }

    @staticmethod
    def from_dict(data):
        """从字典创建模型实例"""
        config = DislikeConversationConfig(
            code=data.get('code'),
            name=data.get('name')
        )
        if 'id' in data:
            config.id = data['id']
        return config 