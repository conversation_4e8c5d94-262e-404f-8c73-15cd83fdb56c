对接阿里云百炼智能体对接的接口，用python实现，这是实现的流程

# 对接百炼应用

## 最简单的示例
```  
from dashscope import Application
response = Application.call(
    api_key='api_key',  
    app_id='YOUR_APP_ID'
    messages=[{
      {"role": "user", "content": "你好"}, 
    }])
```
其中用到的参数
api_key：API key，Config.BAILIAN_CONFIG['api_key']
app_id：应用的标识，Config.BAILIAN_CONFIG['app_id']
messages：历史对话组成的消息列表，来自接口的入参，必传项，格式要求是数组，里面内容不做校验
workspace:  业务空间标识，Config.BAILIAN_CONFIG['workspace']
stream：是否流式输出回复，固定用true
incremental_output：是否增量输出，固定用true
image_list: 图片链接列表，来自接口的入参，非必传，格式要求是数组


这是响应对象：
```
{
    "code": // 表示错误码，调用成功时为空值
    "message": // 表示错误详细信息，请求成功则忽略
    "output": { // 表示调用结果信息
   },
    "usage": { // 表示本次请求使用的数据信
    },
    "request_id": 当前的请求ID
}
```

成功响应: HTTP状态码是200
```
{
    "request_id": "f97ee37d-0f9c-9b93-b6bf-bd263a232bf9",
    "output": {
        "finish_reason": "stop",
        "session_id": "6105c965c31b40958a43dc93c28c7a59",
        "text": "我是通义千问，由阿里云开发的AI助手。我被设计用来回答各种问题、提供信息和与用户进行对话。有什么我可以帮助你的吗？"
    },
    "usage": {
        "models": [
            {
                "output_tokens": 36,
                "model_id": "qwen-plus",
                "input_tokens": 74
            }
        ]
    },
}
```
失败响应：
```
{
  	"request_id": "54dc32fd-968b-4aed-b6a8-ae63d6fda4d5",
  	"code": "InvalidApiKey",
  	"message": "The API key in your request is invalid."
}
```

## 流式输出示例，参考下如何对接的，不要照搬
```
import os
from http import HTTPStatus
from dashscope import Application
response = Application.call(
    # 若没有配置环境变量，可用百炼API Key将下行替换为：api_key="sk-xxx"。但不建议在生产环境中直接将API Key硬编码到代码中，以减少API Key泄露风险。
    api_key=os.getenv("DASHSCOPE_API_KEY"),
    app_id='YOUR_APP_ID',# 替换为实际的应用 ID
    prompt='你是谁？')

if response.status_code != HTTPStatus.OK:
    print(f'request_id={response.request_id}')
    print(f'code={response.status_code}')
    print(f'message={response.message}')
    print(f'请参考文档：https://help.aliyun.com/zh/model-studio/developer-reference/error-code')
else:
    print(response.output.text)
```


上述是如何对接阿里云百炼，需要做的是对阿里云百炼做一层封装，达到转发的效果，比如接口入参接收到messages,image_list，调阿里云百炼的接口，把阿里云百炼返回的当成接口的返回，注意：返回要做成流式的且只做成流式流式的，非流式不考虑
