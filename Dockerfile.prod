FROM python:3.9-alpine

# 安装系统依赖和时区包
RUN apk add --no-cache gcc musl-dev python3-dev tzdata libffi-dev ffmpeg

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置环境变量
ENV FLASK_ENV=production
ENV PORT=5001

WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 使用国内镜像源安装依赖
RUN pip install -i https://pypi.tuna.tsinghua.edu.cn/simple --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 5001

CMD ["python", "run.py"] 