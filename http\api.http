POST  http://**************:5000/api/chat
Content-Type: application/json

{
  "messages": [
    {
      "role": "user",
      "content": "你是谁",
      "reasoning": "string"
    }
  ],
  "stream": false,
  "temperature": 1,
  "provider": "spark"
}

### 生成图片
POST  http://**************:5000/api/media/generate/image
Content-Type: application/json

{
  "prompt": "一个美丽的女孩在海边散步",
}

###
### 阿里云文件上传API
### 
### 功能说明：
### 1. 上传文件到阿里云服务器获取file_id
### 2. 自动轮询检查文件状态，直到状态为FINISH、EXPIRED或ERROR
### 3. 如果状态为PARSING，会每5秒轮询一次，最多尝试30次（2.5分钟）
### 4. 只有状态为FINISH的file_id才表示文件可用
###
### 请求方式：POST multipart/form-data
### 请求参数：file - 要上传的文件
### 
### 响应格式：
### {
###   "success": true/false,
###   "message": "响应消息",
###   "file_id": "阿里云文件ID"（仅成功时返回）
### }
###
POST  http://**************:5000/api/tools/upload_aliyun_file
Content-Type: multipart/form-data; boundary=boundary

--boundary
Content-Disposition: form-data; name="file"; filename="test.txt"
Content-Type: text/plain

这是一个测试文件的内容
--boundary--

### 简单测试用例（小文本文件）
POST  http://localhost:5000/api/tools/upload_aliyun_file
Content-Type: multipart/form-data; boundary=WebKitFormBoundary7MA4YWxkTrZu0gW

--WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="simple_test.txt"
Content-Type: text/plain

Hello World
--WebKitFormBoundary7MA4YWxkTrZu0gW--
