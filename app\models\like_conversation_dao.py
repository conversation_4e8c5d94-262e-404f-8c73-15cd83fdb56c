from app.models.like_conversation import LikeConversation
from app.models.database import SessionLocal
from sqlalchemy.exc import SQLAlchemyError

class LikeConversationDAO:
    """点赞对话记录数据访问对象"""

    @staticmethod
    def get_like_conversations_by_conversation_id(conversation_id):
        """获取指定对话ID的所有点赞记录"""
        db = SessionLocal()
        try:
            like_conversations = db.query(LikeConversation).filter(
                LikeConversation.conversation_id == conversation_id
            ).all()
            return [like_conversation.to_dict() for like_conversation in like_conversations]
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def create_like_conversation(like_conversation_data):
        """创建新的点赞记录"""
        db = SessionLocal()
        try:
            like_conversation = LikeConversation.from_dict(like_conversation_data)
            db.add(like_conversation)
            db.commit()
            db.refresh(like_conversation)
            return like_conversation.to_dict()
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def get_like_conversation_by_id(like_conversation_id):
        """根据ID获取点赞记录"""
        db = SessionLocal()
        try:
            like_conversation = db.query(LikeConversation).filter(
                LikeConversation.id == like_conversation_id
            ).first()
            if like_conversation:
                return like_conversation.to_dict()
            return None
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close()

    @staticmethod
    def delete_like_conversation(like_conversation_id):
        """删除点赞记录"""
        db = SessionLocal()
        try:
            like_conversation = db.query(LikeConversation).filter(
                LikeConversation.id == like_conversation_id
            ).first()
            if not like_conversation:
                return False
            
            db.delete(like_conversation)
            db.commit()
            return True
        except SQLAlchemyError as e:
            db.rollback()
            raise e
        finally:
            db.close() 