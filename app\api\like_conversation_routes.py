from flask import Blueprint, request, jsonify
from flask_restx import Api, Resource, Namespace, fields
from app.models.like_conversation_dao import LikeConversationDAO
import json

# 创建命名空间
like_conversation_ns = Namespace('like_conversations', description='点赞对话记录管理接口')

# 定义消息模型
message_model = like_conversation_ns.model('LikeMessage', {
    'role': fields.String(required=True, description='消息类型 (assistant/user)'),
    'content': fields.String(required=True, description='消息内容'),
    'reasoning': fields.String(required=False, description='推理过程')
})

# 定义点赞对话记录模型
like_conversation_model = like_conversation_ns.model('LikeConversation', {
    'conversationId': fields.Integer(required=True, description='对话记录ID'),
    'messages': fields.List(fields.Nested(message_model), required=True, description='消息列表')
})

# 定义响应模型
response_model = like_conversation_ns.model('Response', {
    'code': fields.Integer(required=True, description='响应代码'),
    'message': fields.String(required=True, description='响应消息'),
    'data': fields.Raw(description='响应数据')
})

@like_conversation_ns.route('')
class LikeConversationList(Resource):
    @like_conversation_ns.doc('点赞对话记录')
    @like_conversation_ns.expect(like_conversation_model)
    @like_conversation_ns.marshal_with(response_model)
    def post(self):
        """点赞对话记录"""
        try:
            data = request.json
            
            # 验证必要参数
            if 'conversationId' not in data or 'messages' not in data:
                return {
                    'code': 400,
                    'message': '缺少必要参数',
                    'data': None
                }, 400
            
            # 创建点赞记录
            like_conversation = LikeConversationDAO.create_like_conversation(data)
            
            return {
                'code': 200,
                'message': '点赞对话记录成功',
                'data': None
            }, 200
        except Exception as e:
            return {
                'code': 500,
                'message': f'服务器错误: {str(e)}',
                'data': None
            }, 500

@like_conversation_ns.route('/conversation/<int:conversation_id>')
@like_conversation_ns.param('conversation_id', '对话记录ID')
class LikeConversationByConversation(Resource):
    @like_conversation_ns.doc('获取指定对话的点赞记录')
    @like_conversation_ns.marshal_with(response_model)
    def get(self, conversation_id):
        """获取指定对话的点赞记录"""
        try:
            # 获取点赞记录
            like_conversations = LikeConversationDAO.get_like_conversations_by_conversation_id(conversation_id)
            
            # 返回完整的点赞记录信息，包括id和messages
            result = []
            for like in like_conversations:
                result.append({
                    'id': like['id'],
                    'messages': like['messages']
                })
            
            return {
                'code': 200,
                'message': '查询点赞对话记录成功',
                'data': result
            }, 200
        except Exception as e:
            return {
                'code': 500,
                'message': f'服务器错误: {str(e)}',
                'data': None
            }, 500

@like_conversation_ns.route('/<int:like_id>')
@like_conversation_ns.param('like_id', '点赞记录ID')
class LikeConversationItem(Resource):
    @like_conversation_ns.doc('获取点赞记录详情')
    @like_conversation_ns.marshal_with(response_model)
    def get(self, like_id):
        """获取点赞记录详情"""
        try:
            # 获取点赞记录
            like_conversation = LikeConversationDAO.get_like_conversation_by_id(like_id)
            
            if not like_conversation:
                return {
                    'code': 404,
                    'message': '点赞记录不存在',
                    'data': None
                }, 404
            
            # 返回完整的点赞记录信息
            result = {
                'id': like_conversation['id'],
                'messages': like_conversation['messages']
            }
            
            return {
                'code': 200,
                'message': '查询点赞记录成功',
                'data': result
            }, 200
        except Exception as e:
            return {
                'code': 500,
                'message': f'服务器错误: {str(e)}',
                'data': None
            }, 500
            
    @like_conversation_ns.doc('取消点赞对话记录')
    @like_conversation_ns.marshal_with(response_model)
    def delete(self, like_id):
        """取消点赞对话记录"""
        try:
            # 删除点赞记录
            result = LikeConversationDAO.delete_like_conversation(like_id)
            
            if not result:
                return {
                    'code': 404,
                    'message': '点赞记录不存在',
                    'data': None
                }, 404
            
            return {
                'code': 200,
                'message': '取消点赞对话记录成功',
                'data': None
            }, 200
        except Exception as e:
            return {
                'code': 500,
                'message': f'服务器错误: {str(e)}',
                'data': None
            }, 500 